/**
 * Customer Factory
 * 为测试创建客户相关的测试数据
 */

const mongoose = require('mongoose');
const Customer = require('../../models/customer');
const CustomerAddress = require('../../models/customerAddress');

/**
 * 创建测试客户
 * @param {Object} overrides - 覆盖默认值的属性
 * @returns {Promise<Object>} 创建的客户对象
 */
const createCustomer = async (overrides = {}) => {
  const defaultCustomer = {
    name: 'Test Customer',
    email: '<EMAIL>',
    phone: '+1234567890',
    phoneIsVerified: false,
    emailIsVerified: false,
    picture: 'https://example.com/avatar.jpg',
    isActive: true,
    addresses: [],
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const customerData = { ...defaultCustomer, ...overrides };
  
  try {
    const customer = new Customer(customerData);
    await customer.save();
    return customer;
  } catch (error) {
    console.error('Error creating test customer:', error);
    throw error;
  }
};

/**
 * 创建测试客户地址
 * @param {Object} overrides - 覆盖默认值的属性
 * @returns {Promise<Object>} 创建的地址对象
 */
const createCustomerAddress = async (overrides = {}) => {
  const defaultAddress = {
    customer: new mongoose.Types.ObjectId(),
    label: 'Home',
    deliveryAddress: '123 Test Street, Test City, TC 12345',
    details: 'Apartment 4B, Ring doorbell',
    location: {
      type: 'Point',
      coordinates: ['-73.935242', '40.730610'] // NYC coordinates
    },
    selected: false,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const addressData = { ...defaultAddress, ...overrides };
  
  try {
    const address = new CustomerAddress(addressData);
    await address.save();
    return address;
  } catch (error) {
    console.error('Error creating test customer address:', error);
    throw error;
  }
};

/**
 * 创建带有地址的完整客户
 * @param {Object} customerOverrides - 客户属性覆盖
 * @param {Array} addressesData - 地址数据数组
 * @returns {Promise<Object>} 包含客户和地址的对象
 */
const createCustomerWithAddresses = async (customerOverrides = {}, addressesData = []) => {
  try {
    const customer = await createCustomer(customerOverrides);
    
    const addresses = [];
    if (addressesData.length === 0) {
      // 创建默认地址
      addressesData = [
        { label: 'Home', selected: true },
        { label: 'Work', selected: false }
      ];
    }

    for (const addressData of addressesData) {
      const address = await createCustomerAddress({
        customer: customer._id,
        ...addressData
      });
      addresses.push(address);
    }

    return {
      customer,
      addresses
    };
  } catch (error) {
    console.error('Error creating customer with addresses:', error);
    throw error;
  }
};

/**
 * 创建已验证的客户
 * @param {Object} overrides - 覆盖默认值的属性
 * @returns {Promise<Object>} 创建的已验证客户对象
 */
const createVerifiedCustomer = async (overrides = {}) => {
  const verifiedOverrides = {
    phoneIsVerified: true,
    emailIsVerified: true,
    ...overrides
  };

  return await createCustomer(verifiedOverrides);
};

/**
 * 创建多个测试客户
 * @param {number} count - 要创建的客户数量
 * @param {Object} baseOverrides - 基础覆盖属性
 * @returns {Promise<Array>} 创建的客户数组
 */
const createMultipleCustomers = async (count = 3, baseOverrides = {}) => {
  const customers = [];
  
  for (let i = 0; i < count; i++) {
    const customer = await createCustomer({
      name: `Test Customer ${i + 1}`,
      email: `test.customer${i + 1}@example.com`,
      phone: `+123456789${i}`,
      ...baseOverrides
    });
    customers.push(customer);
  }
  
  return customers;
};

/**
 * 创建客户订单历史数据
 * @param {string} customerId - 客户ID
 * @param {number} orderCount - 订单数量
 * @returns {Promise<Array>} 创建的订单数组
 */
const createCustomerOrderHistory = async (customerId, orderCount = 5) => {
  const { createOrder } = require('./orderFactory');
  const { createRestaurant } = require('./restaurantFactory');
  
  const orders = [];
  const restaurant = await createRestaurant();
  
  for (let i = 0; i < orderCount; i++) {
    const order = await createOrder({
      user: customerId,
      restaurant: restaurant._id,
      orderStatus: i % 2 === 0 ? 'DELIVERED' : 'PENDING',
      orderAmount: Math.floor(Math.random() * 50) + 10,
      createdAt: new Date(Date.now() - (i * 24 * 60 * 60 * 1000)) // i days ago
    });
    orders.push(order);
  }
  
  return orders;
};

/**
 * 创建客户偏好设置
 * @param {string} customerId - 客户ID
 * @param {Object} preferences - 偏好设置
 * @returns {Promise<Object>} 创建的偏好设置对象
 */
const createCustomerPreferences = async (customerId, preferences = {}) => {
  const defaultPreferences = {
    customer: customerId,
    notifications: {
      email: true,
      sms: true,
      push: true
    },
    dietary: {
      vegetarian: false,
      vegan: false,
      glutenFree: false,
      halal: false
    },
    delivery: {
      defaultTip: 15,
      preferredTime: 'ASAP',
      specialInstructions: ''
    },
    ...preferences
  };

  // Note: This assumes a CustomerPreferences model exists
  // If not, this would need to be adjusted based on actual schema
  return defaultPreferences;
};

/**
 * 创建客户支付方式
 * @param {string} customerId - 客户ID
 * @param {Object} paymentData - 支付方式数据
 * @returns {Promise<Object>} 创建的支付方式对象
 */
const createCustomerPaymentMethod = async (customerId, paymentData = {}) => {
  const defaultPaymentMethod = {
    customer: customerId,
    type: 'CARD',
    cardLast4: '1234',
    cardBrand: 'VISA',
    isDefault: true,
    isActive: true,
    ...paymentData
  };

  // Note: This assumes a CustomerPaymentMethod model exists
  // If not, this would need to be adjusted based on actual schema
  return defaultPaymentMethod;
};

/**
 * 清理测试客户数据
 * @param {Array} customerIds - 要清理的客户ID数组
 */
const cleanupCustomers = async (customerIds = []) => {
  try {
    if (customerIds.length > 0) {
      await Customer.deleteMany({ _id: { $in: customerIds } });
      await CustomerAddress.deleteMany({ customer: { $in: customerIds } });
    } else {
      // 清理所有测试客户（基于邮箱模式）
      await Customer.deleteMany({ email: /test\.customer.*@example\.com/ });
      await CustomerAddress.deleteMany({});
    }
  } catch (error) {
    console.error('Error cleaning up test customers:', error);
    throw error;
  }
};

/**
 * 生成随机客户数据
 * @returns {Object} 随机客户数据
 */
const generateRandomCustomerData = () => {
  const randomId = Math.floor(Math.random() * 10000);
  return {
    name: `Random Customer ${randomId}`,
    email: `random.customer${randomId}@example.com`,
    phone: `+1${Math.floor(Math.random() * 9000000000) + 1000000000}`,
    picture: `https://example.com/avatar${randomId}.jpg`
  };
};

module.exports = {
  createCustomer,
  createCustomerAddress,
  createCustomerWithAddresses,
  createVerifiedCustomer,
  createMultipleCustomers,
  createCustomerOrderHistory,
  createCustomerPreferences,
  createCustomerPaymentMethod,
  cleanupCustomers,
  generateRandomCustomerData
};
