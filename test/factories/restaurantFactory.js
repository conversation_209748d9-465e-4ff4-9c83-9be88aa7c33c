/**
 * Restaurant factory for generating test data
 */

const { faker } = require('@faker-js/faker');
const mongoose = require('mongoose');

// Import Restaurant model
const Restaurant = require('../../models/restaurant');

class RestaurantFactory {
  /**
   * Build a restaurant object without saving to database
   * @param {Object} overrides - Properties to override defaults
   * @returns {Object} Restaurant object
   */
  static build(overrides = {}) {
    return {
      name: faker.company.name(),
      address: faker.location.streetAddress(), // Restaurant model expects a string, not an object
      image: faker.image.url(),
      logo: faker.image.url(),
      orderPrefix: faker.string.alpha(3).toUpperCase(),
      orderId: 1,
      deliveryTime: faker.number.int({ min: 15, max: 60 }),
      minimumOrder: faker.number.float({ min: 5, max: 25, multipleOf: 0.01 }),
      isActive: true,
      location: {
        type: 'Point',
        coordinates: [
          parseFloat(faker.location.longitude()),
          parseFloat(faker.location.latitude())
        ]
      },
      username: faker.internet.userName(),
      password: faker.internet.password(),
      sections: [],
      enableNotification: true,
      isAvailable: true,
      commissionRate: 25,
      cuisines: [faker.helpers.arrayElement(['Italian', 'Chinese', 'Mexican', 'Indian', 'Japanese'])],
      tax: 10,
      reviewCount: 0,
      reviewAverage: 0,
      keywords: [],
      tags: [],
      phone: faker.phone.number(),
      deliveryCostType: 'fixed',
      deliveryCostMin: 3.0,
      _testData: true, // Mark as test data for easy cleanup
      ...overrides
    };
  }
  
  /**
   * Create a restaurant and save to database
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created restaurant
   */
  static async create(overrides = {}) {
    // Ensure mongoose is connected before creating
    const mongoose = require('mongoose');
    if (mongoose.connection.readyState === 0) {
      // Try to connect if not already connected
      const { connectTestDB } = require('../helpers/dbHelper');
      await connectTestDB();
    }

    const restaurantData = this.build(overrides);
    return await Restaurant.create(restaurantData);
  }
  
  /**
   * Create multiple restaurants
   * @param {number} count - Number of restaurants to create
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Array>} Created restaurants
   */
  static async createMany(count, overrides = {}) {
    const restaurants = [];
    
    for (let i = 0; i < count; i++) {
      restaurants.push(this.build(overrides));
    }
    
    return await Restaurant.insertMany(restaurants);
  }
  
  /**
   * Create a restaurant with menu items
   * @param {Object} overrides - Properties to override defaults
   * @param {number} itemCount - Number of menu items to create
   * @returns {Promise<Object>} Created restaurant with menu items
   */
  static async createWithMenu(overrides = {}, itemCount = 5) {
    // This is a placeholder - actual implementation would depend on
    // how menu items are stored in the database
    const restaurant = await this.create(overrides);
    
    // Create menu items for the restaurant
    // This would need to be implemented based on the actual model structure
    
    return restaurant;
  }
}

module.exports = RestaurantFactory;
