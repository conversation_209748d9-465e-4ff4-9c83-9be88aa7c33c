/**
 * Load Testing Suite
 * 测试系统在正常负载下的性能表现
 */

const autocannon = require('autocannon');
const { connectTestDB, disconnectTestDB, clearTestDB } = require('../helpers/testDatabase');
const { createCustomer } = require('../factories/customerFactory');
const { createRestaurant } = require('../factories/restaurantFactory');
const { generateAuthToken } = require('../helpers/authHelper');
const app = require('../../app');

describe('Load Testing', () => {
  let server;
  let testCustomer;
  let testRestaurant;
  let authToken;
  let baseURL;

  beforeAll(async () => {
    // 连接测试数据库
    await connectTestDB({ useRealDatabase: true });
    
    // 启动服务器
    const { httpServer } = await app;
    server = httpServer;
    const port = server.address().port;
    baseURL = `http://localhost:${port}`;
    
    // 创建测试数据
    testCustomer = await createCustomer();
    testRestaurant = await createRestaurant();
    authToken = generateAuthToken(testCustomer._id);
  });

  afterAll(async () => {
    if (server) {
      server.close();
    }
    await disconnectTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
  });

  describe('API Endpoint Load Tests', () => {
    test('should handle 100 concurrent requests to GET /api/orders', async () => {
      const result = await autocannon({
        url: `${baseURL}/api/orders/customer/${testCustomer._id}`,
        connections: 100,
        duration: 10, // 10 seconds
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      // 验证性能指标
      expect(result.errors).toBe(0);
      expect(result.timeouts).toBe(0);
      expect(result.non2xx).toBe(0);
      expect(result.latency.mean).toBeLessThan(500); // 平均响应时间 < 500ms
      expect(result.requests.mean).toBeGreaterThan(50); // 每秒至少50个请求
      
      console.log('Load Test Results:', {
        requests: result.requests,
        latency: result.latency,
        throughput: result.throughput,
        errors: result.errors
      });
    });

    test('should handle order creation under load', async () => {
      const orderData = {
        restaurantId: testRestaurant._id.toString(),
        customerId: testCustomer._id.toString(),
        items: [
          {
            foodId: 'food_1',
            quantity: 1,
            price: 12.99
          }
        ],
        deliveryAddress: '123 Test Street',
        paymentMethod: 'CARD'
      };

      const result = await autocannon({
        url: `${baseURL}/api/orders`,
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData),
        connections: 50,
        duration: 10
      });

      expect(result.errors).toBe(0);
      expect(result.timeouts).toBe(0);
      expect(result.latency.mean).toBeLessThan(1000); // 创建订单 < 1s
      expect(result.requests.mean).toBeGreaterThan(20); // 每秒至少20个订单创建
    });

    test('should handle payment processing under load', async () => {
      const paymentData = {
        orderId: 'test-order-123',
        method: 'STRIPE',
        amount: 25.99,
        currency: 'USD',
        paymentData: {
          paymentMethodId: 'pm_test_123'
        }
      };

      const result = await autocannon({
        url: `${baseURL}/api/payments/process`,
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(paymentData),
        connections: 30,
        duration: 10
      });

      expect(result.errors).toBe(0);
      expect(result.timeouts).toBe(0);
      expect(result.latency.mean).toBeLessThan(2000); // 支付处理 < 2s
    });
  });

  describe('GraphQL Load Tests', () => {
    test('should handle GraphQL queries under load', async () => {
      const query = `
        query {
          restaurants {
            _id
            name
            isActive
          }
        }
      `;

      const result = await autocannon({
        url: `${baseURL}/graphql`,
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query }),
        connections: 100,
        duration: 10
      });

      expect(result.errors).toBe(0);
      expect(result.timeouts).toBe(0);
      expect(result.latency.mean).toBeLessThan(300); // GraphQL查询 < 300ms
    });

    test('should handle complex GraphQL queries under load', async () => {
      const query = `
        query GetOrdersWithDetails($restaurantId: ID!) {
          orders(restaurantId: $restaurantId) {
            _id
            orderId
            orderAmount
            orderStatus
            items {
              _id
              title
              quantity
            }
            user {
              _id
              name
              email
            }
          }
        }
      `;

      const result = await autocannon({
        url: `${baseURL}/graphql`,
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query,
          variables: { restaurantId: testRestaurant._id.toString() }
        }),
        connections: 50,
        duration: 10
      });

      expect(result.errors).toBe(0);
      expect(result.timeouts).toBe(0);
      expect(result.latency.mean).toBeLessThan(800); // 复杂查询 < 800ms
    });
  });

  describe('Database Load Tests', () => {
    test('should handle concurrent database operations', async () => {
      const promises = [];
      const concurrentOperations = 100;

      // 创建100个并发的数据库操作
      for (let i = 0; i < concurrentOperations; i++) {
        promises.push(
          createCustomer({
            email: `load-test-${i}@example.com`,
            name: `Load Test User ${i}`
          })
        );
      }

      const startTime = Date.now();
      const results = await Promise.all(promises);
      const endTime = Date.now();

      const duration = endTime - startTime;
      const operationsPerSecond = (concurrentOperations / duration) * 1000;

      expect(results.length).toBe(concurrentOperations);
      expect(duration).toBeLessThan(5000); // 5秒内完成
      expect(operationsPerSecond).toBeGreaterThan(20); // 每秒至少20个操作

      console.log(`Database Load Test: ${concurrentOperations} operations in ${duration}ms (${operationsPerSecond.toFixed(2)} ops/sec)`);
    });
  });

  describe('Memory and Resource Tests', () => {
    test('should not have memory leaks under sustained load', async () => {
      const initialMemory = process.memoryUsage();
      
      // 运行持续负载测试
      const result = await autocannon({
        url: `${baseURL}/api/orders/customer/${testCustomer._id}`,
        connections: 50,
        duration: 30, // 30秒持续负载
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      // 强制垃圾回收
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      const memoryIncreasePercent = (memoryIncrease / initialMemory.heapUsed) * 100;

      expect(result.errors).toBe(0);
      expect(memoryIncreasePercent).toBeLessThan(50); // 内存增长不超过50%

      console.log('Memory Usage:', {
        initial: `${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`,
        final: `${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`,
        increase: `${(memoryIncrease / 1024 / 1024).toFixed(2)} MB (${memoryIncreasePercent.toFixed(2)}%)`
      });
    });
  });

  describe('Error Rate Tests', () => {
    test('should maintain low error rate under high load', async () => {
      const result = await autocannon({
        url: `${baseURL}/api/orders/customer/${testCustomer._id}`,
        connections: 200, // 高并发
        duration: 15,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      const errorRate = (result.errors / result.requests.total) * 100;
      const timeoutRate = (result.timeouts / result.requests.total) * 100;

      expect(errorRate).toBeLessThan(1); // 错误率 < 1%
      expect(timeoutRate).toBeLessThan(0.5); // 超时率 < 0.5%

      console.log('Error Rate Analysis:', {
        totalRequests: result.requests.total,
        errors: result.errors,
        timeouts: result.timeouts,
        errorRate: `${errorRate.toFixed(2)}%`,
        timeoutRate: `${timeoutRate.toFixed(2)}%`
      });
    });
  });
});

// 性能基准测试
describe('Performance Benchmarks', () => {
  test('should meet performance SLA requirements', async () => {
    const endpoints = [
      {
        name: 'Order List',
        url: '/api/orders/customer/' + testCustomer._id,
        method: 'GET',
        maxLatency: 500,
        minThroughput: 100
      },
      {
        name: 'Payment Methods',
        url: '/api/payments/methods',
        method: 'GET',
        maxLatency: 200,
        minThroughput: 200
      }
    ];

    for (const endpoint of endpoints) {
      const result = await autocannon({
        url: `${baseURL}${endpoint.url}`,
        method: endpoint.method,
        connections: 50,
        duration: 10,
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      expect(result.latency.mean).toBeLessThan(endpoint.maxLatency);
      expect(result.requests.mean).toBeGreaterThan(endpoint.minThroughput);

      console.log(`${endpoint.name} Performance:`, {
        latency: `${result.latency.mean}ms (max: ${endpoint.maxLatency}ms)`,
        throughput: `${result.requests.mean} req/s (min: ${endpoint.minThroughput} req/s)`,
        status: result.latency.mean < endpoint.maxLatency && result.requests.mean > endpoint.minThroughput ? 'PASS' : 'FAIL'
      });
    }
  });
});
