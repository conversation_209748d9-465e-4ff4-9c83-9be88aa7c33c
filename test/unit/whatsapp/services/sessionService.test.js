/**
 * Session Service Unit Tests
 * 测试会话管理服务的核心功能
 */

const SessionService = require('../../../../whatsapp/services/sessionService');
const redis = require('redis');
const logger = require('../../../../helpers/logger');

// Mock dependencies
jest.mock('redis', () => ({
  createClient: jest.fn().mockReturnValue({
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    ttl: jest.fn(),
    on: jest.fn(),
    isReady: true
  })
}));

jest.mock('../../../../helpers/logger', () => ({
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}));

describe('SessionService', () => {
  let sessionService;
  let mockRedisClient;
  const mockConfig = {
    REDIS_URL: 'redis://localhost:6379',
    REDIS_PASSWORD: 'test-password',
    SESSION_TTL: 86400 // 24 hours
  };

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Get mock redis client
    mockRedisClient = redis.createClient();
    
    // Create fresh service instance
    sessionService = new SessionService(mockConfig);
    sessionService.client = mockRedisClient;
  });

  describe('constructor()', () => {
    test('should initialize with correct configuration', () => {
      expect(sessionService.redisUrl).toBe(mockConfig.REDIS_URL);
      expect(sessionService.redisPassword).toBe(mockConfig.REDIS_PASSWORD);
      expect(sessionService.sessionTTL).toBe(mockConfig.SESSION_TTL);
    });

    test('should set default TTL if not provided', () => {
      const defaultService = new SessionService({
        REDIS_URL: 'redis://localhost:6379'
      });
      
      expect(defaultService.sessionTTL).toBe(86400); // 24 hours default
    });
  });

  describe('connect()', () => {
    test('should connect to Redis successfully', async () => {
      await sessionService.connect();

      expect(redis.createClient).toHaveBeenCalledWith({
        url: mockConfig.REDIS_URL,
        password: mockConfig.REDIS_PASSWORD
      });
      expect(mockRedisClient.connect).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith('Connected to Redis for session management');
    });

    test('should handle Redis connection errors', async () => {
      mockRedisClient.connect.mockRejectedValueOnce(new Error('Connection failed'));

      await expect(sessionService.connect()).rejects.toThrow('Connection failed');
      expect(logger.error).toHaveBeenCalledWith(
        'Failed to connect to Redis:',
        expect.any(Error)
      );
    });

    test('should setup error event handlers', async () => {
      await sessionService.connect();

      expect(mockRedisClient.on).toHaveBeenCalledWith('error', expect.any(Function));
      expect(mockRedisClient.on).toHaveBeenCalledWith('connect', expect.any(Function));
      expect(mockRedisClient.on).toHaveBeenCalledWith('ready', expect.any(Function));
    });
  });

  describe('createSession()', () => {
    const mockSessionData = {
      dialogueId: 'dialogue-123',
      customerPhone: '+1234567890',
      brandWhatsappId: 'brand-123',
      brandRef: { id: 'brand-123', name: 'Test Brand' },
      context: {
        customer: { phone: '+1234567890' },
        selectedRestaurantRef: null,
        currentOrder: null
      }
    };

    test('should create session successfully', async () => {
      mockRedisClient.set.mockResolvedValueOnce('OK');
      mockRedisClient.expire.mockResolvedValueOnce(1);

      const session = await sessionService.createSession(
        mockSessionData.dialogueId,
        mockSessionData.customerPhone,
        mockSessionData.brandWhatsappId,
        mockSessionData.brandRef
      );

      expect(session.id).toBe(mockSessionData.dialogueId);
      expect(session.customerPhone).toBe(mockSessionData.customerPhone);
      expect(session.brandWhatsappId).toBe(mockSessionData.brandWhatsappId);
      expect(session.context).toBeDefined();
      expect(session.createdAt).toBeDefined();
      expect(session.updatedAt).toBeDefined();

      expect(mockRedisClient.set).toHaveBeenCalledWith(
        `session:${mockSessionData.dialogueId}`,
        expect.any(String)
      );
      expect(mockRedisClient.expire).toHaveBeenCalledWith(
        `session:${mockSessionData.dialogueId}`,
        mockConfig.SESSION_TTL
      );
    });

    test('should initialize context with custom data', async () => {
      const customContext = {
        selectedRestaurantRef: { id: 'restaurant-123' },
        currentOrder: { items: [] }
      };

      mockRedisClient.set.mockResolvedValueOnce('OK');
      mockRedisClient.expire.mockResolvedValueOnce(1);

      const session = await sessionService.createSession(
        mockSessionData.dialogueId,
        mockSessionData.customerPhone,
        mockSessionData.brandWhatsappId,
        mockSessionData.brandRef,
        customContext
      );

      expect(session.context.selectedRestaurantRef).toEqual(customContext.selectedRestaurantRef);
      expect(session.context.currentOrder).toEqual(customContext.currentOrder);
    });

    test('should handle Redis set errors', async () => {
      mockRedisClient.set.mockRejectedValueOnce(new Error('Redis error'));

      await expect(sessionService.createSession(
        mockSessionData.dialogueId,
        mockSessionData.customerPhone,
        mockSessionData.brandWhatsappId,
        mockSessionData.brandRef
      )).rejects.toThrow('Redis error');

      expect(logger.error).toHaveBeenCalledWith(
        'Error creating session:',
        expect.any(Error)
      );
    });
  });

  describe('getSession()', () => {
    test('should retrieve existing session', async () => {
      const mockSessionJson = JSON.stringify({
        id: 'dialogue-123',
        customerPhone: '+1234567890',
        brandWhatsappId: 'brand-123',
        context: { customer: { phone: '+1234567890' } },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      mockRedisClient.get.mockResolvedValueOnce(mockSessionJson);

      const session = await sessionService.getSession('dialogue-123');

      expect(mockRedisClient.get).toHaveBeenCalledWith('session:dialogue-123');
      expect(session.id).toBe('dialogue-123');
      expect(session.customerPhone).toBe('+1234567890');
      expect(session.context).toBeDefined();
    });

    test('should return null for non-existent session', async () => {
      mockRedisClient.get.mockResolvedValueOnce(null);

      const session = await sessionService.getSession('non-existent');

      expect(session).toBeNull();
      expect(mockRedisClient.get).toHaveBeenCalledWith('session:non-existent');
    });

    test('should handle malformed session data', async () => {
      mockRedisClient.get.mockResolvedValueOnce('invalid-json');

      await expect(sessionService.getSession('dialogue-123'))
        .rejects.toThrow();

      expect(logger.error).toHaveBeenCalledWith(
        'Error parsing session data:',
        expect.any(Error)
      );
    });

    test('should handle Redis get errors', async () => {
      mockRedisClient.get.mockRejectedValueOnce(new Error('Redis error'));

      await expect(sessionService.getSession('dialogue-123'))
        .rejects.toThrow('Redis error');
    });
  });

  describe('updateSession()', () => {
    test('should update existing session', async () => {
      const existingSession = {
        id: 'dialogue-123',
        customerPhone: '+1234567890',
        brandWhatsappId: 'brand-123',
        context: { customer: { phone: '+1234567890' } },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const updates = {
        context: {
          ...existingSession.context,
          selectedRestaurantRef: { id: 'restaurant-123' }
        }
      };

      mockRedisClient.get.mockResolvedValueOnce(JSON.stringify(existingSession));
      mockRedisClient.set.mockResolvedValueOnce('OK');
      mockRedisClient.expire.mockResolvedValueOnce(1);

      const updatedSession = await sessionService.updateSession('dialogue-123', updates);

      expect(updatedSession.context.selectedRestaurantRef).toEqual({ id: 'restaurant-123' });
      expect(updatedSession.updatedAt).not.toBe(existingSession.updatedAt);
      
      expect(mockRedisClient.set).toHaveBeenCalledWith(
        'session:dialogue-123',
        expect.any(String)
      );
    });

    test('should throw error if session does not exist', async () => {
      mockRedisClient.get.mockResolvedValueOnce(null);

      await expect(sessionService.updateSession('non-existent', {}))
        .rejects.toThrow('Session not found');
    });
  });

  describe('deleteSession()', () => {
    test('should delete existing session', async () => {
      mockRedisClient.del.mockResolvedValueOnce(1);

      const result = await sessionService.deleteSession('dialogue-123');

      expect(mockRedisClient.del).toHaveBeenCalledWith('session:dialogue-123');
      expect(result).toBe(true);
    });

    test('should return false if session does not exist', async () => {
      mockRedisClient.del.mockResolvedValueOnce(0);

      const result = await sessionService.deleteSession('non-existent');

      expect(result).toBe(false);
    });

    test('should handle Redis delete errors', async () => {
      mockRedisClient.del.mockRejectedValueOnce(new Error('Redis error'));

      await expect(sessionService.deleteSession('dialogue-123'))
        .rejects.toThrow('Redis error');
    });
  });

  describe('initializeContext()', () => {
    test('should return default context when no custom context provided', () => {
      const context = sessionService.initializeContext();

      expect(context).toEqual({
        customer: null,
        brandRef: null,
        selectedRestaurantRef: null,
        currentOrder: null,
        currentOrderState: {},
        isAddressSelected: false,
        orderPlaced: false,
        paymentDone: false,
        completedOrders: [],
        cartReceived: false,
        selectedAddressIndex: null
      });
    });

    test('should merge custom context with defaults', () => {
      const customContext = {
        customer: { phone: '+1234567890' },
        selectedRestaurantRef: { id: 'restaurant-123' },
        customField: 'custom-value'
      };

      const context = sessionService.initializeContext(customContext);

      expect(context.customer).toEqual(customContext.customer);
      expect(context.selectedRestaurantRef).toEqual(customContext.selectedRestaurantRef);
      expect(context.customField).toBe('custom-value');
      expect(context.currentOrder).toBeNull(); // Default value preserved
      expect(context.orderPlaced).toBe(false); // Default value preserved
    });

    test('should not override defaults with undefined values', () => {
      const customContext = {
        customer: { phone: '+1234567890' },
        currentOrder: undefined,
        orderPlaced: undefined
      };

      const context = sessionService.initializeContext(customContext);

      expect(context.customer).toEqual(customContext.customer);
      expect(context.currentOrder).toBeNull(); // Default preserved
      expect(context.orderPlaced).toBe(false); // Default preserved
    });
  });

  describe('Session.toJSON()', () => {
    test('should serialize session correctly', () => {
      const session = {
        id: 'dialogue-123',
        customerPhone: '+1234567890',
        brandWhatsappId: 'brand-123',
        context: {
          customer: { phone: '+1234567890' },
          selectedRestaurantRef: { id: 'restaurant-123' }
        },
        createdAt: new Date('2023-01-01T00:00:00.000Z'),
        updatedAt: new Date('2023-01-01T01:00:00.000Z')
      };

      const json = JSON.stringify(session);
      const parsed = JSON.parse(json);

      expect(parsed.id).toBe(session.id);
      expect(parsed.customerPhone).toBe(session.customerPhone);
      expect(parsed.brandWhatsappId).toBe(session.brandWhatsappId);
      expect(parsed.context).toEqual(session.context);
      expect(parsed.createdAt).toBe('2023-01-01T00:00:00.000Z');
      expect(parsed.updatedAt).toBe('2023-01-01T01:00:00.000Z');
    });

    test('should handle complex context objects', () => {
      const session = {
        id: 'dialogue-123',
        context: {
          currentOrder: {
            orderInput: [
              {
                food: 'food-123',
                variation: 'variation-123',
                quantity: 2,
                addons: ['addon-1', 'addon-2'],
                specialInstructions: 'No onions'
              }
            ],
            taxationAmount: 1.50,
            tipping: 2.00
          },
          completedOrders: [
            { orderId: 'order-1', amount: 15.99 },
            { orderId: 'order-2', amount: 23.50 }
          ]
        }
      };

      const json = JSON.stringify(session);
      const parsed = JSON.parse(json);

      expect(parsed.context.currentOrder).toEqual(session.context.currentOrder);
      expect(parsed.context.completedOrders).toEqual(session.context.completedOrders);
    });
  });

  describe('TTL and expiration handling', () => {
    test('should check session TTL', async () => {
      mockRedisClient.ttl.mockResolvedValueOnce(3600); // 1 hour remaining

      const ttl = await sessionService.getSessionTTL('dialogue-123');

      expect(mockRedisClient.ttl).toHaveBeenCalledWith('session:dialogue-123');
      expect(ttl).toBe(3600);
    });

    test('should return -1 for non-existent session TTL', async () => {
      mockRedisClient.ttl.mockResolvedValueOnce(-2); // Key does not exist

      const ttl = await sessionService.getSessionTTL('non-existent');

      expect(ttl).toBe(-2);
    });

    test('should extend session TTL', async () => {
      mockRedisClient.expire.mockResolvedValueOnce(1);

      const result = await sessionService.extendSessionTTL('dialogue-123', 7200);

      expect(mockRedisClient.expire).toHaveBeenCalledWith('session:dialogue-123', 7200);
      expect(result).toBe(true);
    });

    test('should handle TTL extension for non-existent session', async () => {
      mockRedisClient.expire.mockResolvedValueOnce(0);

      const result = await sessionService.extendSessionTTL('non-existent', 7200);

      expect(result).toBe(false);
    });
  });

  describe('Connection management', () => {
    test('should disconnect from Redis gracefully', async () => {
      await sessionService.disconnect();

      expect(mockRedisClient.disconnect).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith('Disconnected from Redis');
    });

    test('should handle disconnect errors', async () => {
      mockRedisClient.disconnect.mockRejectedValueOnce(new Error('Disconnect error'));

      await expect(sessionService.disconnect()).rejects.toThrow('Disconnect error');
    });

    test('should check Redis connection status', () => {
      const isConnected = sessionService.isConnected();

      expect(isConnected).toBe(true);
    });
  });
});
