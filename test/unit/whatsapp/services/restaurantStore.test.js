/**
 * Restaurant Store Unit Tests
 * 测试餐厅数据存储服务的核心功能
 */

const RestaurantStore = require('../../../../whatsapp/services/restaurantStore');
const Restaurant = require('../../../../models/restaurant');
const Brand = require('../../../../models/brand');
const logger = require('../../../../helpers/logger');

// Mock dependencies
jest.mock('../../../../models/restaurant');
jest.mock('../../../../models/brand');
jest.mock('../../../../helpers/logger', () => ({
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}));

describe('RestaurantStore', () => {
  let restaurantStore;
  
  const mockRestaurants = [
    {
      _id: 'restaurant-1',
      name: 'Test Restaurant 1',
      brandId: 'brand-1',
      isActive: true,
      location: {
        coordinates: [-73.935242, 40.730610]
      },
      categories: [
        {
          _id: 'category-1',
          title: 'Main Dishes',
          foods: [
            {
              _id: 'food-1',
              title: 'Burger',
              price: 12.99,
              variations: [
                {
                  _id: 'variation-1',
                  title: 'Regular',
                  price: 12.99,
                  discounted: 0
                }
              ]
            }
          ]
        }
      ],
      addons: [],
      options: [],
      deliveryCostType: 'fixed',
      deliveryCostMin: 3.00
    },
    {
      _id: 'restaurant-2',
      name: 'Test Restaurant 2',
      brandId: 'brand-1',
      isActive: true,
      location: {
        coordinates: [-73.945242, 40.740610]
      },
      categories: [],
      addons: [],
      options: []
    }
  ];

  const mockBrands = [
    {
      _id: 'brand-1',
      name: 'Test Brand',
      whatsappId: 'whatsapp-1',
      isActive: true
    },
    {
      _id: 'brand-2',
      name: 'Another Brand',
      whatsappId: 'whatsapp-2',
      isActive: true
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create fresh store instance
    restaurantStore = new RestaurantStore();
    
    // Setup default mocks
    Restaurant.find.mockResolvedValue(mockRestaurants);
    Restaurant.findById.mockImplementation((id) => {
      const restaurant = mockRestaurants.find(r => r._id === id);
      return Promise.resolve(restaurant || null);
    });
    
    Brand.find.mockResolvedValue(mockBrands);
    Brand.findById.mockImplementation((id) => {
      const brand = mockBrands.find(b => b._id === id);
      return Promise.resolve(brand || null);
    });
  });

  describe('initialize()', () => {
    test('should initialize cache with data from callback', async () => {
      const mockDataCallback = jest.fn().mockResolvedValue({
        restaurants: mockRestaurants,
        brands: mockBrands
      });

      await restaurantStore.initialize(mockDataCallback);

      expect(mockDataCallback).toHaveBeenCalled();
      expect(restaurantStore.restaurantsCache.size).toBe(2);
      expect(restaurantStore.brandsCache.size).toBe(2);
      expect(restaurantStore.isInitialized).toBe(true);
      expect(logger.info).toHaveBeenCalledWith('Restaurant store initialized with 2 restaurants and 2 brands');
    });

    test('should handle initialization errors gracefully', async () => {
      const mockDataCallback = jest.fn().mockRejectedValue(new Error('Database error'));

      await expect(restaurantStore.initialize(mockDataCallback))
        .rejects.toThrow('Database error');

      expect(restaurantStore.isInitialized).toBe(false);
      expect(logger.error).toHaveBeenCalledWith(
        'Failed to initialize restaurant store:',
        expect.any(Error)
      );
    });

    test('should not reinitialize if already initialized', async () => {
      const mockDataCallback = jest.fn().mockResolvedValue({
        restaurants: mockRestaurants,
        brands: mockBrands
      });

      // First initialization
      await restaurantStore.initialize(mockDataCallback);
      expect(mockDataCallback).toHaveBeenCalledTimes(1);

      // Second initialization attempt
      await restaurantStore.initialize(mockDataCallback);
      expect(mockDataCallback).toHaveBeenCalledTimes(1); // Should not be called again
    });

    test('should handle empty data gracefully', async () => {
      const mockDataCallback = jest.fn().mockResolvedValue({
        restaurants: [],
        brands: []
      });

      await restaurantStore.initialize(mockDataCallback);

      expect(restaurantStore.restaurantsCache.size).toBe(0);
      expect(restaurantStore.brandsCache.size).toBe(0);
      expect(restaurantStore.isInitialized).toBe(true);
    });
  });

  describe('getRestaurantById()', () => {
    beforeEach(async () => {
      const mockDataCallback = jest.fn().mockResolvedValue({
        restaurants: mockRestaurants,
        brands: mockBrands
      });
      await restaurantStore.initialize(mockDataCallback);
    });

    test('should return restaurant from cache', async () => {
      const restaurant = await restaurantStore.getRestaurantById('restaurant-1');

      expect(restaurant).toEqual(mockRestaurants[0]);
      expect(Restaurant.findById).not.toHaveBeenCalled(); // Should not hit database
    });

    test('should return null for non-existent restaurant', async () => {
      const restaurant = await restaurantStore.getRestaurantById('non-existent');

      expect(restaurant).toBeNull();
    });

    test('should fallback to database if not in cache', async () => {
      const newRestaurant = {
        _id: 'restaurant-3',
        name: 'New Restaurant',
        brandId: 'brand-1',
        isActive: true
      };

      Restaurant.findById.mockResolvedValueOnce(newRestaurant);

      const restaurant = await restaurantStore.getRestaurantById('restaurant-3');

      expect(Restaurant.findById).toHaveBeenCalledWith('restaurant-3');
      expect(restaurant).toEqual(newRestaurant);
      
      // Should now be cached
      expect(restaurantStore.restaurantsCache.get('restaurant-3')).toEqual(newRestaurant);
    });

    test('should handle database errors in fallback', async () => {
      Restaurant.findById.mockRejectedValueOnce(new Error('Database error'));

      await expect(restaurantStore.getRestaurantById('restaurant-3'))
        .rejects.toThrow('Database error');

      expect(logger.error).toHaveBeenCalledWith(
        'Error fetching restaurant from database:',
        expect.any(Error)
      );
    });

    test('should throw error if store not initialized and no fallback', async () => {
      const uninitializedStore = new RestaurantStore();

      await expect(uninitializedStore.getRestaurantById('restaurant-1'))
        .rejects.toThrow('Restaurant store not initialized');
    });
  });

  describe('getBrandById()', () => {
    beforeEach(async () => {
      const mockDataCallback = jest.fn().mockResolvedValue({
        restaurants: mockRestaurants,
        brands: mockBrands
      });
      await restaurantStore.initialize(mockDataCallback);
    });

    test('should return brand from cache', async () => {
      const brand = await restaurantStore.getBrandById('brand-1');

      expect(brand).toEqual(mockBrands[0]);
      expect(Brand.findById).not.toHaveBeenCalled();
    });

    test('should return null for non-existent brand', async () => {
      const brand = await restaurantStore.getBrandById('non-existent');

      expect(brand).toBeNull();
    });

    test('should fallback to database if not in cache', async () => {
      const newBrand = {
        _id: 'brand-3',
        name: 'New Brand',
        whatsappId: 'whatsapp-3',
        isActive: true
      };

      Brand.findById.mockResolvedValueOnce(newBrand);

      const brand = await restaurantStore.getBrandById('brand-3');

      expect(Brand.findById).toHaveBeenCalledWith('brand-3');
      expect(brand).toEqual(newBrand);
      
      // Should now be cached
      expect(restaurantStore.brandsCache.get('brand-3')).toEqual(newBrand);
    });
  });

  describe('getRestaurantsByBrand()', () => {
    beforeEach(async () => {
      const mockDataCallback = jest.fn().mockResolvedValue({
        restaurants: mockRestaurants,
        brands: mockBrands
      });
      await restaurantStore.initialize(mockDataCallback);
    });

    test('should return restaurants for a brand', () => {
      const restaurants = restaurantStore.getRestaurantsByBrand('brand-1');

      expect(restaurants).toHaveLength(2);
      expect(restaurants[0]._id).toBe('restaurant-1');
      expect(restaurants[1]._id).toBe('restaurant-2');
    });

    test('should return empty array for non-existent brand', () => {
      const restaurants = restaurantStore.getRestaurantsByBrand('non-existent');

      expect(restaurants).toEqual([]);
    });

    test('should filter by active status', () => {
      // Add inactive restaurant to mock data
      const inactiveRestaurant = {
        ...mockRestaurants[0],
        _id: 'restaurant-inactive',
        isActive: false
      };
      restaurantStore.restaurantsCache.set('restaurant-inactive', inactiveRestaurant);

      const restaurants = restaurantStore.getRestaurantsByBrand('brand-1', true);

      expect(restaurants).toHaveLength(2); // Only active restaurants
      expect(restaurants.every(r => r.isActive)).toBe(true);
    });
  });

  describe('cache management', () => {
    test('should refresh cache', async () => {
      const mockDataCallback = jest.fn().mockResolvedValue({
        restaurants: mockRestaurants,
        brands: mockBrands
      });

      await restaurantStore.initialize(mockDataCallback);
      expect(mockDataCallback).toHaveBeenCalledTimes(1);

      // Refresh cache
      await restaurantStore.refreshCache(mockDataCallback);
      expect(mockDataCallback).toHaveBeenCalledTimes(2);
    });

    test('should clear cache', () => {
      restaurantStore.restaurantsCache.set('test', {});
      restaurantStore.brandsCache.set('test', {});

      restaurantStore.clearCache();

      expect(restaurantStore.restaurantsCache.size).toBe(0);
      expect(restaurantStore.brandsCache.size).toBe(0);
      expect(restaurantStore.isInitialized).toBe(false);
    });

    test('should get cache statistics', async () => {
      const mockDataCallback = jest.fn().mockResolvedValue({
        restaurants: mockRestaurants,
        brands: mockBrands
      });
      await restaurantStore.initialize(mockDataCallback);

      const stats = restaurantStore.getCacheStats();

      expect(stats).toEqual({
        restaurantsCount: 2,
        brandsCount: 2,
        isInitialized: true,
        lastUpdated: expect.any(Date)
      });
    });
  });
});
