/**
 * Message Builders Unit Tests
 * 测试消息构建器的核心功能
 */

const messageBuilders = require('../../../../whatsapp/services/messageBuilders');
const { v4: uuidv4 } = require('uuid');

// Mock uuid
jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('mock-uuid-123')
}));

describe('MessageBuilders', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('buildBasicTextMessageData()', () => {
    test('should build basic text message with JSON format', () => {
      const recipientId = 'test-recipient';
      const text = 'Hello, World!';
      const messageType = 'text';

      const result = messageBuilders.buildBasicTextMessageData(recipientId, text, messageType);

      expect(result).toEqual({
        recipient_id: recipientId,
        message: {
          text: text
        },
        message_type: messageType,
        message_id: 'mock-uuid-123'
      });
    });

    test('should build basic text message with FormData format', () => {
      const recipientId = 'test-recipient';
      const text = 'Hello, World!';
      const messageType = 'text';
      const useFormData = true;

      const result = messageBuilders.buildBasicTextMessageData(
        recipientId, 
        text, 
        messageType, 
        useFormData
      );

      expect(result).toBeInstanceOf(FormData);
      // Note: FormData testing requires additional setup for proper verification
    });

    test('should handle empty text message', () => {
      const recipientId = 'test-recipient';
      const text = '';
      const messageType = 'text';

      const result = messageBuilders.buildBasicTextMessageData(recipientId, text, messageType);

      expect(result.message.text).toBe('');
      expect(result.recipient_id).toBe(recipientId);
    });

    test('should handle long text message', () => {
      const recipientId = 'test-recipient';
      const text = 'A'.repeat(4096); // Very long text
      const messageType = 'text';

      const result = messageBuilders.buildBasicTextMessageData(recipientId, text, messageType);

      expect(result.message.text).toBe(text);
      expect(result.message.text.length).toBe(4096);
    });

    test('should handle special characters in text', () => {
      const recipientId = 'test-recipient';
      const text = 'Hello! 🎉 Special chars: @#$%^&*()';
      const messageType = 'text';

      const result = messageBuilders.buildBasicTextMessageData(recipientId, text, messageType);

      expect(result.message.text).toBe(text);
    });

    test('should generate unique message IDs', () => {
      uuidv4
        .mockReturnValueOnce('uuid-1')
        .mockReturnValueOnce('uuid-2');

      const result1 = messageBuilders.buildBasicTextMessageData('recipient1', 'text1', 'text');
      const result2 = messageBuilders.buildBasicTextMessageData('recipient2', 'text2', 'text');

      expect(result1.message_id).toBe('uuid-1');
      expect(result2.message_id).toBe('uuid-2');
      expect(uuidv4).toHaveBeenCalledTimes(2);
    });
  });

  describe('buildWhatsAppTemplateMessageData()', () => {
    test('should build template message with basic options', () => {
      const recipientId = 'test-recipient';
      const templateName = 'welcome_template';
      const options = {
        name: 'John Doe',
        restaurant: 'Test Restaurant'
      };
      const messageType = 'template';

      const result = messageBuilders.buildWhatsAppTemplateMessageData(
        recipientId, 
        templateName, 
        options, 
        messageType
      );

      expect(result).toEqual({
        recipient_id: recipientId,
        message: {
          template: {
            name: templateName,
            language: {
              code: 'en'
            },
            components: expect.any(Array)
          }
        },
        message_type: messageType,
        message_id: 'mock-uuid-123'
      });
    });

    test('should build template message with header parameters', () => {
      const recipientId = 'test-recipient';
      const templateName = 'order_confirmation';
      const options = {
        header: {
          type: 'text',
          parameters: ['Order #12345']
        },
        body: {
          parameters: ['John Doe', 'Test Restaurant', '$25.99']
        }
      };
      const messageType = 'template';

      const result = messageBuilders.buildWhatsAppTemplateMessageData(
        recipientId, 
        templateName, 
        options, 
        messageType
      );

      expect(result.message.template.components).toContainEqual({
        type: 'header',
        parameters: expect.arrayContaining([
          expect.objectContaining({
            type: 'text',
            text: 'Order #12345'
          })
        ])
      });
    });

    test('should build template message with button parameters', () => {
      const recipientId = 'test-recipient';
      const templateName = 'payment_reminder';
      const options = {
        buttons: [
          {
            type: 'url',
            index: 0,
            parameters: ['https://payment.example.com/order-123']
          }
        ]
      };
      const messageType = 'template';

      const result = messageBuilders.buildWhatsAppTemplateMessageData(
        recipientId, 
        templateName, 
        options, 
        messageType
      );

      expect(result.message.template.components).toContainEqual({
        type: 'button',
        sub_type: 'url',
        index: 0,
        parameters: expect.arrayContaining([
          expect.objectContaining({
            type: 'text',
            text: 'https://payment.example.com/order-123'
          })
        ])
      });
    });

    test('should handle template with custom language', () => {
      const recipientId = 'test-recipient';
      const templateName = 'welcome_template';
      const options = {
        language: 'es',
        name: 'Juan'
      };
      const messageType = 'template';

      const result = messageBuilders.buildWhatsAppTemplateMessageData(
        recipientId, 
        templateName, 
        options, 
        messageType
      );

      expect(result.message.template.language.code).toBe('es');
    });

    test('should handle empty options', () => {
      const recipientId = 'test-recipient';
      const templateName = 'simple_template';
      const options = {};
      const messageType = 'template';

      const result = messageBuilders.buildWhatsAppTemplateMessageData(
        recipientId, 
        templateName, 
        options, 
        messageType
      );

      expect(result.message.template.name).toBe(templateName);
      expect(result.message.template.language.code).toBe('en');
      expect(result.message.template.components).toEqual([]);
    });
  });

  describe('buildWhatsAppFlowMessageData()', () => {
    test('should build flow message with basic options', () => {
      const recipientId = 'test-recipient';
      const options = {
        flow_id: 'flow-123',
        flow_token: 'token-456',
        flow_data: {
          screen: 'menu_selection',
          restaurant_id: 'restaurant-123'
        }
      };
      const messageType = 'flow';

      const result = messageBuilders.buildWhatsAppFlowMessageData(
        recipientId, 
        options, 
        messageType
      );

      expect(result).toEqual({
        recipient_id: recipientId,
        message: {
          type: 'interactive',
          interactive: {
            type: 'flow',
            header: options.header || undefined,
            body: options.body || undefined,
            footer: options.footer || undefined,
            action: {
              name: 'flow',
              parameters: {
                flow_id: options.flow_id,
                flow_token: options.flow_token,
                flow_data: options.flow_data,
                mode: options.mode || 'published'
              }
            }
          }
        },
        message_type: messageType,
        message_id: 'mock-uuid-123'
      });
    });

    test('should build flow message with header, body, and footer', () => {
      const recipientId = 'test-recipient';
      const options = {
        flow_id: 'flow-123',
        flow_token: 'token-456',
        header: {
          type: 'text',
          text: 'Select Your Order'
        },
        body: {
          text: 'Choose items from our menu'
        },
        footer: {
          text: 'Powered by Firespoon'
        },
        flow_data: {
          screen: 'menu_selection'
        }
      };
      const messageType = 'flow';

      const result = messageBuilders.buildWhatsAppFlowMessageData(
        recipientId, 
        options, 
        messageType
      );

      expect(result.message.interactive.header).toEqual(options.header);
      expect(result.message.interactive.body).toEqual(options.body);
      expect(result.message.interactive.footer).toEqual(options.footer);
    });

    test('should handle draft mode flow', () => {
      const recipientId = 'test-recipient';
      const options = {
        flow_id: 'flow-123',
        flow_token: 'token-456',
        mode: 'draft',
        flow_data: {}
      };
      const messageType = 'flow';

      const result = messageBuilders.buildWhatsAppFlowMessageData(
        recipientId, 
        options, 
        messageType
      );

      expect(result.message.interactive.action.parameters.mode).toBe('draft');
    });

    test('should require flow_id and flow_token', () => {
      const recipientId = 'test-recipient';
      const options = {
        flow_data: {}
      };
      const messageType = 'flow';

      expect(() => {
        messageBuilders.buildWhatsAppFlowMessageData(recipientId, options, messageType);
      }).toThrow();
    });
  });

  describe('Message format validation', () => {
    test('should validate JSON message format', () => {
      const result = messageBuilders.buildBasicTextMessageData(
        'recipient', 
        'text', 
        'text'
      );

      // Should be a plain object, not FormData
      expect(typeof result).toBe('object');
      expect(result.constructor.name).toBe('Object');
      expect(result.recipient_id).toBeDefined();
      expect(result.message).toBeDefined();
      expect(result.message_type).toBeDefined();
      expect(result.message_id).toBeDefined();
    });

    test('should ensure message ID uniqueness across different builders', () => {
      uuidv4
        .mockReturnValueOnce('uuid-text')
        .mockReturnValueOnce('uuid-template')
        .mockReturnValueOnce('uuid-flow');

      const textMessage = messageBuilders.buildBasicTextMessageData('r1', 'text', 'text');
      const templateMessage = messageBuilders.buildWhatsAppTemplateMessageData('r2', 'template', {}, 'template');
      const flowMessage = messageBuilders.buildWhatsAppFlowMessageData('r3', { flow_id: 'f1', flow_token: 't1' }, 'flow');

      expect(textMessage.message_id).toBe('uuid-text');
      expect(templateMessage.message_id).toBe('uuid-template');
      expect(flowMessage.message_id).toBe('uuid-flow');
    });
  });
});
