/**
 * Session ID Generator Unit Tests
 * 测试会话ID生成器的核心功能
 */

const sessionIdGenerator = require('../../../../whatsapp/utils/sessionIdGenerator');

describe('SessionIdGenerator', () => {
  describe('generateSessionId()', () => {
    test('should generate a session ID', () => {
      const sessionId = sessionIdGenerator.generateSessionId();

      expect(sessionId).toBeDefined();
      expect(typeof sessionId).toBe('string');
      expect(sessionId.length).toBeGreaterThan(0);
    });

    test('should generate unique session IDs', () => {
      const sessionId1 = sessionIdGenerator.generateSessionId();
      const sessionId2 = sessionIdGenerator.generateSessionId();

      expect(sessionId1).not.toBe(sessionId2);
    });

    test('should generate session IDs with correct format', () => {
      const sessionId = sessionIdGenerator.generateSessionId();

      // Should be alphanumeric with possible hyphens (UUID format)
      expect(sessionId).toMatch(/^[a-f0-9-]+$/i);
    });

    test('should generate session IDs of consistent length', () => {
      const sessionIds = Array.from({ length: 10 }, () => 
        sessionIdGenerator.generateSessionId()
      );

      const lengths = sessionIds.map(id => id.length);
      const uniqueLengths = [...new Set(lengths)];

      expect(uniqueLengths.length).toBe(1); // All should have same length
    });
  });

  describe('generateDialogueId()', () => {
    test('should generate a dialogue ID', () => {
      const dialogueId = sessionIdGenerator.generateDialogueId();

      expect(dialogueId).toBeDefined();
      expect(typeof dialogueId).toBe('string');
      expect(dialogueId.length).toBeGreaterThan(0);
    });

    test('should generate unique dialogue IDs', () => {
      const dialogueId1 = sessionIdGenerator.generateDialogueId();
      const dialogueId2 = sessionIdGenerator.generateDialogueId();

      expect(dialogueId1).not.toBe(dialogueId2);
    });

    test('should generate dialogue IDs with correct prefix', () => {
      const dialogueId = sessionIdGenerator.generateDialogueId();

      expect(dialogueId).toMatch(/^dlg_/);
    });
  });

  describe('generateMessageId()', () => {
    test('should generate a message ID', () => {
      const messageId = sessionIdGenerator.generateMessageId();

      expect(messageId).toBeDefined();
      expect(typeof messageId).toBe('string');
      expect(messageId.length).toBeGreaterThan(0);
    });

    test('should generate unique message IDs', () => {
      const messageId1 = sessionIdGenerator.generateMessageId();
      const messageId2 = sessionIdGenerator.generateMessageId();

      expect(messageId1).not.toBe(messageId2);
    });

    test('should generate message IDs with correct prefix', () => {
      const messageId = sessionIdGenerator.generateMessageId();

      expect(messageId).toMatch(/^msg_/);
    });
  });

  describe('generateCustomId()', () => {
    test('should generate custom ID with prefix', () => {
      const prefix = 'test';
      const customId = sessionIdGenerator.generateCustomId(prefix);

      expect(customId).toMatch(new RegExp(`^${prefix}_`));
    });

    test('should generate custom ID without prefix', () => {
      const customId = sessionIdGenerator.generateCustomId();

      expect(customId).toBeDefined();
      expect(typeof customId).toBe('string');
      expect(customId.length).toBeGreaterThan(0);
    });

    test('should handle empty prefix', () => {
      const customId = sessionIdGenerator.generateCustomId('');

      expect(customId).toBeDefined();
      expect(customId).not.toMatch(/^_/); // Should not start with underscore
    });

    test('should handle special characters in prefix', () => {
      const prefix = 'test-prefix_123';
      const customId = sessionIdGenerator.generateCustomId(prefix);

      expect(customId).toMatch(new RegExp(`^${prefix}_`));
    });
  });

  describe('ID uniqueness testing', () => {
    test('should generate unique IDs across large number of calls', () => {
      const numberOfIds = 10000;
      const sessionIds = new Set();

      for (let i = 0; i < numberOfIds; i++) {
        const id = sessionIdGenerator.generateSessionId();
        sessionIds.add(id);
      }

      expect(sessionIds.size).toBe(numberOfIds);
    });

    test('should generate unique IDs across different types', () => {
      const sessionId = sessionIdGenerator.generateSessionId();
      const dialogueId = sessionIdGenerator.generateDialogueId();
      const messageId = sessionIdGenerator.generateMessageId();

      expect(sessionId).not.toBe(dialogueId);
      expect(sessionId).not.toBe(messageId);
      expect(dialogueId).not.toBe(messageId);
    });

    test('should maintain uniqueness across concurrent calls', async () => {
      const numberOfConcurrentCalls = 1000;
      const promises = Array.from({ length: numberOfConcurrentCalls }, () =>
        Promise.resolve(sessionIdGenerator.generateSessionId())
      );

      const sessionIds = await Promise.all(promises);
      const uniqueIds = new Set(sessionIds);

      expect(uniqueIds.size).toBe(numberOfConcurrentCalls);
    });
  });

  describe('ID format validation', () => {
    test('should generate IDs that are URL-safe', () => {
      const sessionId = sessionIdGenerator.generateSessionId();
      const encoded = encodeURIComponent(sessionId);

      expect(encoded).toBe(sessionId); // Should not need encoding
    });

    test('should generate IDs without spaces', () => {
      const sessionId = sessionIdGenerator.generateSessionId();

      expect(sessionId).not.toContain(' ');
    });

    test('should generate IDs without special characters that need escaping', () => {
      const sessionId = sessionIdGenerator.generateSessionId();

      // Should not contain characters that need escaping in JSON or URLs
      expect(sessionId).not.toMatch(/[<>"'&\\]/);
    });

    test('should generate IDs that are database-safe', () => {
      const sessionId = sessionIdGenerator.generateSessionId();

      // Should not contain SQL injection characters
      expect(sessionId).not.toMatch(/[';--]/);
    });
  });

  describe('Performance testing', () => {
    test('should generate IDs quickly', () => {
      const startTime = Date.now();
      const numberOfIds = 1000;

      for (let i = 0; i < numberOfIds; i++) {
        sessionIdGenerator.generateSessionId();
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should generate 1000 IDs in less than 100ms
      expect(duration).toBeLessThan(100);
    });

    test('should have consistent performance', () => {
      const measurements = [];

      for (let i = 0; i < 10; i++) {
        const startTime = Date.now();
        
        for (let j = 0; j < 100; j++) {
          sessionIdGenerator.generateSessionId();
        }
        
        const endTime = Date.now();
        measurements.push(endTime - startTime);
      }

      const average = measurements.reduce((a, b) => a + b, 0) / measurements.length;
      const variance = measurements.reduce((acc, val) => acc + Math.pow(val - average, 2), 0) / measurements.length;
      const standardDeviation = Math.sqrt(variance);

      // Standard deviation should be low (consistent performance)
      expect(standardDeviation).toBeLessThan(average * 0.5);
    });
  });

  describe('Edge cases', () => {
    test('should handle rapid successive calls', () => {
      const ids = [];
      
      for (let i = 0; i < 100; i++) {
        ids.push(sessionIdGenerator.generateSessionId());
      }

      const uniqueIds = new Set(ids);
      expect(uniqueIds.size).toBe(100);
    });

    test('should work in different execution contexts', () => {
      // Test in setTimeout to simulate different execution context
      return new Promise((resolve) => {
        setTimeout(() => {
          const id1 = sessionIdGenerator.generateSessionId();
          const id2 = sessionIdGenerator.generateSessionId();
          
          expect(id1).not.toBe(id2);
          expect(id1).toBeDefined();
          expect(id2).toBeDefined();
          
          resolve();
        }, 0);
      });
    });

    test('should handle memory pressure gracefully', () => {
      // Generate many IDs to test memory usage
      const ids = [];
      
      for (let i = 0; i < 50000; i++) {
        ids.push(sessionIdGenerator.generateSessionId());
      }

      // Check that we can still generate unique IDs
      const newId = sessionIdGenerator.generateSessionId();
      expect(ids).not.toContain(newId);
    });
  });
});
