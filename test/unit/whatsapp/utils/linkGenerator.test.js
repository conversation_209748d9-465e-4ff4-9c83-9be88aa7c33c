/**
 * Link Generator Unit Tests
 * 测试链接生成器的核心功能
 */

const linkGenerator = require('../../../../whatsapp/utils/linkGenerator');

describe('LinkGenerator', () => {
  const mockConfig = {
    FRONTEND_URL: 'https://app.firespoon.com',
    PAYMENT_URL: 'https://payment.firespoon.com',
    API_URL: 'https://api.firespoon.com'
  };

  beforeEach(() => {
    // Mock process.env or config
    process.env.FRONTEND_URL = mockConfig.FRONTEND_URL;
    process.env.PAYMENT_URL = mockConfig.PAYMENT_URL;
    process.env.API_URL = mockConfig.API_URL;
  });

  describe('generateMenuLink()', () => {
    test('should generate menu link with session token', () => {
      const sessionToken = 'session-123';
      const restaurantId = 'restaurant-456';

      const link = linkGenerator.generateMenuLink(sessionToken, restaurantId);

      expect(link).toBe(
        `${mockConfig.FRONTEND_URL}/menu/${restaurantId}?token=${sessionToken}`
      );
    });

    test('should generate menu link without restaurant ID', () => {
      const sessionToken = 'session-123';

      const link = linkGenerator.generateMenuLink(sessionToken);

      expect(link).toBe(
        `${mockConfig.FRONTEND_URL}/menu?token=${sessionToken}`
      );
    });

    test('should handle special characters in session token', () => {
      const sessionToken = 'session-123+special/chars=';
      const restaurantId = 'restaurant-456';

      const link = linkGenerator.generateMenuLink(sessionToken, restaurantId);

      expect(link).toBe(
        `${mockConfig.FRONTEND_URL}/menu/${restaurantId}?token=${encodeURIComponent(sessionToken)}`
      );
    });

    test('should handle empty session token', () => {
      const sessionToken = '';
      const restaurantId = 'restaurant-456';

      const link = linkGenerator.generateMenuLink(sessionToken, restaurantId);

      expect(link).toBe(
        `${mockConfig.FRONTEND_URL}/menu/${restaurantId}?token=`
      );
    });
  });

  describe('generatePaymentLink()', () => {
    test('should generate payment link with order ID', () => {
      const orderId = 'order-789';
      const sessionToken = 'session-123';

      const link = linkGenerator.generatePaymentLink(orderId, sessionToken);

      expect(link).toBe(
        `${mockConfig.PAYMENT_URL}/checkout/${orderId}?token=${sessionToken}`
      );
    });

    test('should generate payment link with additional parameters', () => {
      const orderId = 'order-789';
      const sessionToken = 'session-123';
      const additionalParams = {
        returnUrl: 'https://app.firespoon.com/orders',
        method: 'stripe'
      };

      const link = linkGenerator.generatePaymentLink(orderId, sessionToken, additionalParams);

      expect(link).toBe(
        `${mockConfig.PAYMENT_URL}/checkout/${orderId}?token=${sessionToken}&returnUrl=${encodeURIComponent(additionalParams.returnUrl)}&method=stripe`
      );
    });

    test('should handle missing session token', () => {
      const orderId = 'order-789';

      const link = linkGenerator.generatePaymentLink(orderId);

      expect(link).toBe(
        `${mockConfig.PAYMENT_URL}/checkout/${orderId}`
      );
    });

    test('should handle special characters in order ID', () => {
      const orderId = 'order-789+special/chars';
      const sessionToken = 'session-123';

      const link = linkGenerator.generatePaymentLink(orderId, sessionToken);

      expect(link).toBe(
        `${mockConfig.PAYMENT_URL}/checkout/${encodeURIComponent(orderId)}?token=${sessionToken}`
      );
    });
  });

  describe('generateAddressManagementLink()', () => {
    test('should generate address management link', () => {
      const sessionToken = 'session-123';
      const customerId = 'customer-456';

      const link = linkGenerator.generateAddressManagementLink(sessionToken, customerId);

      expect(link).toBe(
        `${mockConfig.FRONTEND_URL}/addresses?token=${sessionToken}&customerId=${customerId}`
      );
    });

    test('should generate address management link without customer ID', () => {
      const sessionToken = 'session-123';

      const link = linkGenerator.generateAddressManagementLink(sessionToken);

      expect(link).toBe(
        `${mockConfig.FRONTEND_URL}/addresses?token=${sessionToken}`
      );
    });

    test('should generate address edit link', () => {
      const sessionToken = 'session-123';
      const customerId = 'customer-456';
      const addressId = 'address-789';

      const link = linkGenerator.generateAddressManagementLink(sessionToken, customerId, addressId);

      expect(link).toBe(
        `${mockConfig.FRONTEND_URL}/addresses/${addressId}?token=${sessionToken}&customerId=${customerId}`
      );
    });
  });

  describe('generateOrderTrackingLink()', () => {
    test('should generate order tracking link', () => {
      const orderId = 'order-789';
      const sessionToken = 'session-123';

      const link = linkGenerator.generateOrderTrackingLink(orderId, sessionToken);

      expect(link).toBe(
        `${mockConfig.FRONTEND_URL}/orders/${orderId}?token=${sessionToken}`
      );
    });

    test('should generate order tracking link without session token', () => {
      const orderId = 'order-789';

      const link = linkGenerator.generateOrderTrackingLink(orderId);

      expect(link).toBe(
        `${mockConfig.FRONTEND_URL}/orders/${orderId}`
      );
    });
  });

  describe('generateWebhookUrl()', () => {
    test('should generate webhook URL', () => {
      const webhookType = 'payment';
      const identifier = 'order-123';

      const url = linkGenerator.generateWebhookUrl(webhookType, identifier);

      expect(url).toBe(
        `${mockConfig.API_URL}/webhooks/${webhookType}/${identifier}`
      );
    });

    test('should generate webhook URL without identifier', () => {
      const webhookType = 'whatsapp';

      const url = linkGenerator.generateWebhookUrl(webhookType);

      expect(url).toBe(
        `${mockConfig.API_URL}/webhooks/${webhookType}`
      );
    });
  });

  describe('URL parameter encoding', () => {
    test('should properly encode URL parameters', () => {
      const sessionToken = 'session+with/special=chars&more';
      const restaurantId = 'restaurant-456';

      const link = linkGenerator.generateMenuLink(sessionToken, restaurantId);

      expect(link).toContain(encodeURIComponent(sessionToken));
      expect(link).not.toContain('+');
      expect(link).not.toContain('/');
      expect(link).not.toContain('=');
      expect(link).not.toContain('&');
    });

    test('should handle multiple special characters', () => {
      const orderId = 'order@#$%^&*()';
      const sessionToken = 'session!@#$%^&*()';

      const link = linkGenerator.generatePaymentLink(orderId, sessionToken);

      expect(link).toContain(encodeURIComponent(orderId));
      expect(link).toContain(encodeURIComponent(sessionToken));
    });

    test('should handle Unicode characters', () => {
      const sessionToken = 'session-测试-🎉';
      const restaurantId = 'restaurant-456';

      const link = linkGenerator.generateMenuLink(sessionToken, restaurantId);

      expect(link).toContain(encodeURIComponent(sessionToken));
      expect(decodeURIComponent(link.split('token=')[1])).toBe(sessionToken);
    });
  });

  describe('configuration validation', () => {
    test('should handle missing FRONTEND_URL', () => {
      delete process.env.FRONTEND_URL;

      expect(() => {
        linkGenerator.generateMenuLink('session-123');
      }).toThrow('FRONTEND_URL not configured');
    });

    test('should handle missing PAYMENT_URL', () => {
      delete process.env.PAYMENT_URL;

      expect(() => {
        linkGenerator.generatePaymentLink('order-123', 'session-123');
      }).toThrow('PAYMENT_URL not configured');
    });

    test('should handle missing API_URL', () => {
      delete process.env.API_URL;

      expect(() => {
        linkGenerator.generateWebhookUrl('payment');
      }).toThrow('API_URL not configured');
    });
  });

  describe('link validation', () => {
    test('should generate valid URLs', () => {
      const sessionToken = 'session-123';
      const restaurantId = 'restaurant-456';

      const link = linkGenerator.generateMenuLink(sessionToken, restaurantId);

      expect(() => new URL(link)).not.toThrow();
    });

    test('should generate URLs with correct protocol', () => {
      const sessionToken = 'session-123';
      const restaurantId = 'restaurant-456';

      const link = linkGenerator.generateMenuLink(sessionToken, restaurantId);
      const url = new URL(link);

      expect(url.protocol).toBe('https:');
    });

    test('should generate URLs with correct hostname', () => {
      const sessionToken = 'session-123';
      const restaurantId = 'restaurant-456';

      const link = linkGenerator.generateMenuLink(sessionToken, restaurantId);
      const url = new URL(link);

      expect(url.hostname).toBe('app.firespoon.com');
    });
  });
});
