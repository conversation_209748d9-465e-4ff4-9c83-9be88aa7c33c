/**
 * Test Database Helper
 * 为测试提供数据库连接和清理功能
 */

const mongoose = require('mongoose');

let isConnected = false;

/**
 * 连接到测试数据库
 * @returns {Promise<void>}
 */
const connectTestDB = async () => {
  try {
    if (isConnected) {
      return;
    }

    // 检查是否已经连接
    if (mongoose.connection.readyState === 1) {
      isConnected = true;
      return;
    }

    // 使用环境变量中的测试数据库URL，如果没有则使用默认值
    const mongoUri = process.env.MONGO_URL || 'mongodb://localhost:27017/test';

    // 连接到测试数据库
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    isConnected = true;
    console.log('Connected to test database');
  } catch (error) {
    console.error('Error connecting to test database:', error);
    throw error;
  }
};

/**
 * 断开测试数据库连接
 * @returns {Promise<void>}
 */
const disconnectTestDB = async () => {
  try {
    if (!isConnected) {
      return;
    }

    // 只有在测试环境中才断开连接
    if (process.env.NODE_ENV === 'test') {
      await mongoose.disconnect();
    }

    isConnected = false;
    console.log('Disconnected from test database');
  } catch (error) {
    console.error('Error disconnecting from test database:', error);
    throw error;
  }
};

/**
 * 清理测试数据库
 * @returns {Promise<void>}
 */
const clearTestDB = async () => {
  try {
    if (!isConnected) {
      await connectTestDB();
    }

    const collections = mongoose.connection.collections;
    
    for (const key in collections) {
      const collection = collections[key];
      await collection.deleteMany({});
    }

    console.log('Test database cleared');
  } catch (error) {
    console.error('Error clearing test database:', error);
    throw error;
  }
};

/**
 * 清理特定集合
 * @param {Array<string>} collectionNames - 要清理的集合名称数组
 * @returns {Promise<void>}
 */
const clearCollections = async (collectionNames = []) => {
  try {
    if (!isConnected) {
      await connectTestDB();
    }

    const collections = mongoose.connection.collections;
    
    for (const collectionName of collectionNames) {
      if (collections[collectionName]) {
        await collections[collectionName].deleteMany({});
        console.log(`Cleared collection: ${collectionName}`);
      }
    }
  } catch (error) {
    console.error('Error clearing collections:', error);
    throw error;
  }
};

/**
 * 获取数据库连接状态
 * @returns {boolean} 是否已连接
 */
const isDBConnected = () => {
  return isConnected && mongoose.connection.readyState === 1;
};

/**
 * 等待数据库连接就绪
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise<void>}
 */
const waitForDBConnection = async (timeout = 10000) => {
  const startTime = Date.now();
  
  while (!isDBConnected() && (Date.now() - startTime) < timeout) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  if (!isDBConnected()) {
    throw new Error('Database connection timeout');
  }
};

/**
 * 创建数据库索引
 * @param {string} collectionName - 集合名称
 * @param {Object} indexSpec - 索引规范
 * @param {Object} options - 索引选项
 * @returns {Promise<void>}
 */
const createIndex = async (collectionName, indexSpec, options = {}) => {
  try {
    if (!isConnected) {
      await connectTestDB();
    }

    const collection = mongoose.connection.collection(collectionName);
    await collection.createIndex(indexSpec, options);
    console.log(`Created index on ${collectionName}:`, indexSpec);
  } catch (error) {
    console.error('Error creating index:', error);
    throw error;
  }
};

/**
 * 删除数据库索引
 * @param {string} collectionName - 集合名称
 * @param {string} indexName - 索引名称
 * @returns {Promise<void>}
 */
const dropIndex = async (collectionName, indexName) => {
  try {
    if (!isConnected) {
      await connectTestDB();
    }

    const collection = mongoose.connection.collection(collectionName);
    await collection.dropIndex(indexName);
    console.log(`Dropped index ${indexName} from ${collectionName}`);
  } catch (error) {
    console.error('Error dropping index:', error);
    throw error;
  }
};

/**
 * 获取集合统计信息
 * @param {string} collectionName - 集合名称
 * @returns {Promise<Object>} 统计信息
 */
const getCollectionStats = async (collectionName) => {
  try {
    if (!isConnected) {
      await connectTestDB();
    }

    const collection = mongoose.connection.collection(collectionName);
    const stats = await collection.stats();
    return stats;
  } catch (error) {
    console.error('Error getting collection stats:', error);
    throw error;
  }
};

/**
 * 执行数据库聚合查询
 * @param {string} collectionName - 集合名称
 * @param {Array} pipeline - 聚合管道
 * @returns {Promise<Array>} 查询结果
 */
const aggregate = async (collectionName, pipeline) => {
  try {
    if (!isConnected) {
      await connectTestDB();
    }

    const collection = mongoose.connection.collection(collectionName);
    const result = await collection.aggregate(pipeline).toArray();
    return result;
  } catch (error) {
    console.error('Error executing aggregation:', error);
    throw error;
  }
};

/**
 * 插入测试数据
 * @param {string} collectionName - 集合名称
 * @param {Array|Object} data - 要插入的数据
 * @returns {Promise<Object>} 插入结果
 */
const insertTestData = async (collectionName, data) => {
  try {
    if (!isConnected) {
      await connectTestDB();
    }

    const collection = mongoose.connection.collection(collectionName);
    
    if (Array.isArray(data)) {
      return await collection.insertMany(data);
    } else {
      return await collection.insertOne(data);
    }
  } catch (error) {
    console.error('Error inserting test data:', error);
    throw error;
  }
};

/**
 * 设置测试数据库环境
 * @returns {Promise<void>}
 */
const setupTestEnvironment = async () => {
  try {
    await connectTestDB();
    await clearTestDB();
    console.log('Test environment setup complete');
  } catch (error) {
    console.error('Error setting up test environment:', error);
    throw error;
  }
};

/**
 * 清理测试数据库环境
 * @returns {Promise<void>}
 */
const teardownTestEnvironment = async () => {
  try {
    await clearTestDB();
    await disconnectTestDB();
    console.log('Test environment teardown complete');
  } catch (error) {
    console.error('Error tearing down test environment:', error);
    throw error;
  }
};

module.exports = {
  connectTestDB,
  disconnectTestDB,
  clearTestDB,
  clearCollections,
  isDBConnected,
  waitForDBConnection,
  createIndex,
  dropIndex,
  getCollectionStats,
  aggregate,
  insertTestData,
  setupTestEnvironment,
  teardownTestEnvironment
};
