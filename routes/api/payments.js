/**
 * Payments REST API Routes
 * 为测试提供支付相关的REST API端点
 */

const express = require('express');
const router = express.Router();

// 中间件：验证认证
const requireAuth = (req, res, next) => {
  if (!req.headers.authorization) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  const token = req.headers.authorization.replace('Bearer ', '');
  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  req.userId = 'test-user-id';
  req.isAuth = true;
  next();
};

// 获取可用支付方式
router.get('/methods', requireAuth, async (req, res) => {
  try {
    const methods = [
      {
        name: 'STRIPE',
        displayName: 'Credit/Debit Card',
        enabled: true,
        supportedCurrencies: ['USD', 'EUR', 'GBP']
      },
      {
        name: 'PAYPAL',
        displayName: 'PayPal',
        enabled: true,
        supportedCurrencies: ['USD', 'EUR', 'GBP']
      },
      {
        name: 'CASH',
        displayName: 'Cash on Delivery',
        enabled: true,
        supportedCurrencies: ['USD']
      }
    ];

    res.json({
      success: true,
      methods
    });
  } catch (error) {
    console.error('Error fetching payment methods:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 验证支付方式
router.post('/validate-method', requireAuth, async (req, res) => {
  try {
    const { method, amount, currency } = req.body;

    const validMethods = ['STRIPE', 'PAYPAL', 'CASH'];
    const validCurrencies = ['USD', 'EUR', 'GBP'];

    if (!validMethods.includes(method)) {
      return res.status(400).json({
        valid: false,
        error: 'Invalid payment method'
      });
    }

    if (!validCurrencies.includes(currency)) {
      return res.status(400).json({
        valid: false,
        error: 'Invalid currency'
      });
    }

    if (amount <= 0) {
      return res.status(400).json({
        valid: false,
        error: 'Invalid amount'
      });
    }

    res.json({
      valid: true,
      method,
      amount,
      currency
    });
  } catch (error) {
    console.error('Error validating payment method:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 处理支付
router.post('/process', requireAuth, async (req, res) => {
  try {
    const { orderId, method, amount, currency, paymentData } = req.body;

    // 验证必需字段
    if (!orderId || !method || !amount || !currency) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields'
      });
    }

    // 验证金额
    if (amount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid amount'
      });
    }

    // 验证货币
    if (!['USD', 'EUR', 'GBP'].includes(currency)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid currency'
      });
    }

    // 模拟支付处理
    const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 根据支付方式模拟不同的处理逻辑
    let paymentResult;
    switch (method) {
      case 'STRIPE':
        paymentResult = await processStripePayment(orderId, amount, currency, paymentData);
        break;
      case 'PAYPAL':
        paymentResult = await processPayPalPayment(orderId, amount, currency, paymentData);
        break;
      case 'CASH':
        paymentResult = {
          success: true,
          transactionId,
          status: 'PENDING'
        };
        break;
      default:
        return res.status(400).json({
          success: false,
          error: 'Unsupported payment method'
        });
    }

    if (!paymentResult.success) {
      return res.status(400).json({
        success: false,
        error: paymentResult.error
      });
    }

    res.json({
      success: true,
      method,
      transactionId: paymentResult.transactionId,
      status: paymentResult.status
    });
  } catch (error) {
    console.error('Error processing payment:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取支付状态
router.get('/status/:orderId', requireAuth, async (req, res) => {
  try {
    const { orderId } = req.params;

    // 模拟从数据库获取支付状态
    const paymentStatus = {
      orderId,
      status: 'COMPLETED', // PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED
      transactionId: `txn_${orderId}`,
      amount: 25.99,
      currency: 'USD',
      method: 'STRIPE',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    res.json(paymentStatus);
  } catch (error) {
    console.error('Error fetching payment status:', error);
    res.status(404).json({
      error: 'Order not found'
    });
  }
});

// 更新支付状态
router.put('/status/:orderId', requireAuth, async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status, transactionId, method } = req.body;

    if (!status) {
      return res.status(400).json({
        success: false,
        error: 'Status is required'
      });
    }

    const validStatuses = ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status'
      });
    }

    // 模拟更新支付状态
    const updatedPayment = {
      orderId,
      status,
      transactionId,
      method,
      updatedAt: new Date()
    };

    res.json({
      success: true,
      ...updatedPayment
    });
  } catch (error) {
    console.error('Error updating payment status:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 处理退款
router.post('/refund', requireAuth, async (req, res) => {
  try {
    const { orderId, amount, reason, method, transactionId } = req.body;

    if (!orderId || !amount || !reason) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields'
      });
    }

    // 验证退款金额
    if (amount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid refund amount'
      });
    }

    // 模拟检查原始订单金额
    const originalAmount = 25.99; // 模拟从数据库获取
    if (amount > originalAmount) {
      return res.status(400).json({
        success: false,
        error: 'Refund amount exceeds original payment'
      });
    }

    // 模拟退款处理
    const refundId = `refund_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    res.json({
      success: true,
      refundId,
      amount,
      status: 'COMPLETED',
      reason,
      processedAt: new Date()
    });
  } catch (error) {
    console.error('Error processing refund:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 模拟Stripe支付处理
async function processStripePayment(orderId, amount, currency, paymentData) {
  try {
    // 模拟Stripe API调用
    if (!paymentData || !paymentData.paymentMethodId) {
      return {
        success: false,
        error: 'Payment method ID is required for Stripe payments'
      };
    }

    // 模拟成功的支付
    return {
      success: true,
      transactionId: `pi_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      status: 'COMPLETED'
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

// 模拟PayPal支付处理
async function processPayPalPayment(orderId, amount, currency, paymentData) {
  try {
    // 模拟PayPal API调用
    if (!paymentData || !paymentData.orderID) {
      return {
        success: false,
        error: 'PayPal order ID is required'
      };
    }

    // 模拟成功的支付
    return {
      success: true,
      transactionId: `PAYPAL_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      status: 'COMPLETED'
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = router;
