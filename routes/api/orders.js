/**
 * Orders REST API Routes
 * 为测试提供REST API端点
 */

const express = require('express');
const router = express.Router();
const Order = require('../../models/order');
const Customer = require('../../models/customer');
const Restaurant = require('../../models/restaurant');
const { transformOrder } = require('../../graphql/resolvers/merge');
const { sendNotification } = require('../../helpers/utilities');
const { pubsub, ORDER_STATUS_CHANGED } = require('../../helpers/pubsub');

// 中间件：验证认证
const requireAuth = (req, res, next) => {
  if (!req.headers.authorization) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  // 简单的token验证 - 在实际应用中应该使用JWT验证
  const token = req.headers.authorization.replace('Bearer ', '');
  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  // 这里应该验证token并设置req.userId
  req.userId = 'test-user-id'; // 临时设置
  req.isAuth = true;
  next();
};

// 创建订单
router.post('/', requireAuth, async (req, res) => {
  try {
    const {
      restaurantId,
      customerId,
      items,
      deliveryAddress,
      paymentMethod,
      deliveryCharges = 0,
      taxAmount = 0
    } = req.body;

    // 验证必需字段
    if (!restaurantId || !customerId || !items || !deliveryAddress) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: restaurantId, customerId, items, deliveryAddress'
      });
    }

    // 验证餐厅存在
    const restaurant = await Restaurant.findById(restaurantId);
    if (!restaurant) {
      return res.status(404).json({
        success: false,
        error: 'Restaurant not found'
      });
    }

    // 验证客户存在
    const customer = await Customer.findById(customerId);
    if (!customer) {
      return res.status(404).json({
        success: false,
        error: 'Customer not found'
      });
    }

    // 计算订单总额
    const itemsTotal = items.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);
    const orderAmount = itemsTotal + deliveryCharges + taxAmount;

    // 生成订单ID
    const orderId = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // 创建订单
    const order = new Order({
      orderId,
      restaurant: restaurantId,
      user: customerId,
      items: items.map(item => ({
        ...item,
        _id: item.foodId || `item_${Math.random().toString(36).substr(2, 9)}`
      })),
      deliveryAddress,
      orderAmount,
      deliveryCharges,
      taxationAmount: taxAmount,
      paymentMethod: paymentMethod || 'CARD',
      orderStatus: 'PENDING',
      paymentStatus: 'PENDING',
      orderDate: new Date(),
      isActive: true
    });

    await order.save();

    res.status(201).json({
      success: true,
      order: transformOrder(order)
    });
  } catch (error) {
    console.error('Error creating order:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取订单详情
router.get('/:id', requireAuth, async (req, res) => {
  try {
    const order = await Order.findById(req.params.id)
      .populate('restaurant')
      .populate('user');

    if (!order) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }

    res.json({
      success: true,
      order: transformOrder(order)
    });
  } catch (error) {
    console.error('Error fetching order:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取客户订单列表
router.get('/customer/:customerId', requireAuth, async (req, res) => {
  try {
    const { customerId } = req.params;
    const { page = 1, limit = 10, status } = req.query;

    const query = { user: customerId };
    if (status) {
      query.orderStatus = status;
    }

    const orders = await Order.find(query)
      .populate('restaurant')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(parseInt(limit));

    const total = await Order.countDocuments(query);

    res.json({
      success: true,
      orders: orders.map(transformOrder),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching customer orders:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取餐厅订单列表
router.get('/restaurant/:restaurantId', requireAuth, async (req, res) => {
  try {
    const { restaurantId } = req.params;
    const { page = 1, limit = 10, status } = req.query;

    const query = { restaurant: restaurantId };
    if (status) {
      query.orderStatus = status;
    }

    const orders = await Order.find(query)
      .populate('user')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(parseInt(limit));

    const total = await Order.countDocuments(query);

    res.json({
      success: true,
      orders: orders.map(transformOrder),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching restaurant orders:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 更新订单状态
router.put('/:id/status', requireAuth, async (req, res) => {
  try {
    const { status, estimatedTime, riderId } = req.body;

    if (!status) {
      return res.status(400).json({
        success: false,
        error: 'Status is required'
      });
    }

    const order = await Order.findById(req.params.id);
    if (!order) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }

    // 验证状态转换
    const validTransitions = {
      'PENDING': ['ACCEPTED', 'CANCELLED'],
      'ACCEPTED': ['PREPARING', 'CANCELLED'],
      'PREPARING': ['READY', 'CANCELLED'],
      'READY': ['OUT_FOR_DELIVERY'],
      'OUT_FOR_DELIVERY': ['DELIVERED'],
      'DELIVERED': [],
      'CANCELLED': []
    };

    if (!validTransitions[order.orderStatus].includes(status)) {
      return res.status(400).json({
        success: false,
        error: `Invalid status transition from ${order.orderStatus} to ${status}`
      });
    }

    // 更新订单状态
    order.orderStatus = status;
    
    // 设置时间戳
    const now = new Date();
    switch (status) {
      case 'ACCEPTED':
        order.acceptedAt = now;
        if (estimatedTime) {
          order.estimatedTime = estimatedTime;
          order.expectedTime = new Date(now.getTime() + estimatedTime * 60000);
        }
        break;
      case 'PREPARING':
        order.preparingAt = now;
        break;
      case 'READY':
        order.readyAt = now;
        break;
      case 'OUT_FOR_DELIVERY':
        order.outForDeliveryAt = now;
        if (riderId) {
          order.rider = riderId;
        }
        break;
      case 'DELIVERED':
        order.deliveredAt = now;
        order.paymentStatus = 'COMPLETED';
        break;
      case 'CANCELLED':
        order.cancelledAt = now;
        break;
    }

    await order.save();

    // 发送通知
    try {
      pubsub.publish(ORDER_STATUS_CHANGED, {
        orderStatusChanged: transformOrder(order)
      });
    } catch (notificationError) {
      console.error('Error sending notification:', notificationError);
    }

    res.json({
      success: true,
      order: transformOrder(order),
      notificationsSent: true
    });
  } catch (error) {
    console.error('Error updating order status:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 取消订单
router.put('/:id/cancel', requireAuth, async (req, res) => {
  try {
    const { reason } = req.body;

    if (!reason) {
      return res.status(400).json({
        success: false,
        error: 'Cancellation reason is required'
      });
    }

    const order = await Order.findById(req.params.id);
    if (!order) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }

    // 检查是否可以取消
    if (['DELIVERED', 'CANCELLED'].includes(order.orderStatus)) {
      return res.status(400).json({
        success: false,
        error: 'Order cannot be cancelled'
      });
    }

    order.orderStatus = 'CANCELLED';
    order.cancelReason = reason;
    order.cancelledAt = new Date();

    await order.save();

    res.json({
      success: true,
      order: transformOrder(order),
      notificationsSent: true
    });
  } catch (error) {
    console.error('Error cancelling order:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 添加订单项目
router.post('/:id/items', requireAuth, async (req, res) => {
  try {
    const { items } = req.body;

    if (!items || !Array.isArray(items)) {
      return res.status(400).json({
        success: false,
        error: 'Items array is required'
      });
    }

    const order = await Order.findById(req.params.id);
    if (!order) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }

    // 只有PENDING状态的订单可以修改
    if (order.orderStatus !== 'PENDING') {
      return res.status(400).json({
        success: false,
        error: 'Order cannot be modified after acceptance'
      });
    }

    // 添加新项目
    order.items = items.map(item => ({
      ...item,
      _id: item.foodId || `item_${Math.random().toString(36).substr(2, 9)}`
    }));

    // 重新计算总额
    const itemsTotal = items.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);
    order.orderAmount = itemsTotal + order.deliveryCharges + order.taxationAmount;

    await order.save();

    res.json({
      success: true,
      order: transformOrder(order)
    });
  } catch (error) {
    console.error('Error updating order items:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 更新配送地址
router.put('/:id/address', requireAuth, async (req, res) => {
  try {
    const { deliveryAddress } = req.body;

    if (!deliveryAddress) {
      return res.status(400).json({
        success: false,
        error: 'Delivery address is required'
      });
    }

    const order = await Order.findById(req.params.id);
    if (!order) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }

    // 只有PENDING状态的订单可以修改
    if (order.orderStatus !== 'PENDING') {
      return res.status(400).json({
        success: false,
        error: 'Order cannot be modified after acceptance'
      });
    }

    order.deliveryAddress = deliveryAddress;
    await order.save();

    res.json({
      success: true,
      order: transformOrder(order)
    });
  } catch (error) {
    console.error('Error updating delivery address:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
