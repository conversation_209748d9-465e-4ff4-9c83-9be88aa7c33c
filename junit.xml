<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="7" failures="0" errors="0" time="4.792">
  <testsuite name="WhatsApp Webhook Integration" errors="0" failures="0" skipped="0" timestamp="2025-06-07T17:09:18" time="2.748" tests="3">
    <testcase classname="WhatsApp Webhook Integration should process incoming message and create session" name="WhatsApp Webhook Integration should process incoming message and create session" time="0.699">
    </testcase>
    <testcase classname="WhatsApp Webhook Integration should reject webhook with invalid signature" name="WhatsApp Webhook Integration should reject webhook with invalid signature" time="0.008">
    </testcase>
    <testcase classname="WhatsApp Webhook Integration should handle malformed webhook payload" name="WhatsApp Webhook Integration should handle malformed webhook payload" time="0.007">
    </testcase>
  </testsuite>
  <testsuite name="Order GraphQL Mutations" errors="0" failures="0" skipped="0" timestamp="2025-06-07T17:09:21" time="1.586" tests="4">
    <testcase classname="Order GraphQL Mutations should create a new order" name="Order GraphQL Mutations should create a new order" time="0.274">
    </testcase>
    <testcase classname="Order GraphQL Mutations should fail to create order with invalid restaurant ID" name="Order GraphQL Mutations should fail to create order with invalid restaurant ID" time="0.19">
    </testcase>
    <testcase classname="Order GraphQL Mutations should fail to create order without authentication" name="Order GraphQL Mutations should fail to create order without authentication" time="0.172">
    </testcase>
    <testcase classname="Order GraphQL Mutations should update order status" name="Order GraphQL Mutations should update order status" time="0.18">
    </testcase>
  </testsuite>
</testsuites>