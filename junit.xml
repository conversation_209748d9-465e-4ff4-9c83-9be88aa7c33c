<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="71" failures="0" errors="0" time="15.983">
  <testsuite name="Dialog Manager" errors="0" failures="0" skipped="0" timestamp="2025-06-08T22:17:21" time="12.533" tests="6">
    <testcase classname="Dialog Manager 基础状态转换测试 should transition from initial to restaurant selection on message received" name="Dialog Manager 基础状态转换测试 should transition from initial to restaurant selection on message received" time="0.007">
    </testcase>
    <testcase classname="Dialog Manager 基础状态转换测试 should transition through the order flow" name="Dialog Manager 基础状态转换测试 should transition through the order flow" time="10.089">
    </testcase>
    <testcase classname="Dialog Manager 基础状态转换测试 should handle payment failure" name="Dialog Manager 基础状态转换测试 should handle payment failure" time="0.006">
    </testcase>
    <testcase classname="Dialog Manager 状态机服务生命周期管理 should start and stop dialog manager service" name="Dialog Manager 状态机服务生命周期管理 should start and stop dialog manager service" time="0.003">
    </testcase>
    <testcase classname="Dialog Manager 状态机服务生命周期管理 should properly clean up mocks and reset state" name="Dialog Manager 状态机服务生命周期管理 should properly clean up mocks and reset state" time="0.003">
    </testcase>
    <testcase classname="Dialog Manager 状态机服务生命周期管理 should handle unknown event types gracefully" name="Dialog Manager 状态机服务生命周期管理 should handle unknown event types gracefully" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="SessionService" errors="0" failures="0" skipped="0" timestamp="2025-06-08T22:17:34" time="1.636" tests="11">
    <testcase classname="SessionService Service Initialization should export a service instance" name="SessionService Service Initialization should export a service instance" time="0.24">
    </testcase>
    <testcase classname="SessionService Service Initialization should have required methods" name="SessionService Service Initialization should have required methods" time="0.119">
    </testcase>
    <testcase classname="SessionService Service Initialization should have Redis client" name="SessionService Service Initialization should have Redis client" time="0.101">
    </testcase>
    <testcase classname="SessionService Service Initialization should have session queues map" name="SessionService Service Initialization should have session queues map" time="0.1">
    </testcase>
    <testcase classname="SessionService Basic Functionality should be able to call initializeContext method" name="SessionService Basic Functionality should be able to call initializeContext method" time="0.113">
    </testcase>
    <testcase classname="SessionService Basic Functionality should be able to call createSession method" name="SessionService Basic Functionality should be able to call createSession method" time="0.141">
    </testcase>
    <testcase classname="SessionService Basic Functionality should be able to call getSession method" name="SessionService Basic Functionality should be able to call getSession method" time="0.12">
    </testcase>
    <testcase classname="SessionService Basic Functionality should be able to call updateSession method" name="SessionService Basic Functionality should be able to call updateSession method" time="0.183">
    </testcase>
    <testcase classname="SessionService Basic Functionality should be able to call deleteSession method" name="SessionService Basic Functionality should be able to call deleteSession method" time="0.143">
    </testcase>
    <testcase classname="SessionService Context Initialization should initialize context with proper structure" name="SessionService Context Initialization should initialize context with proper structure" time="0.091">
    </testcase>
    <testcase classname="SessionService Context Initialization should merge custom context with defaults" name="SessionService Context Initialization should merge custom context with defaults" time="0.095">
    </testcase>
  </testsuite>
  <testsuite name="RestaurantStore" errors="0" failures="0" skipped="0" timestamp="2025-06-08T22:17:36" time="0.328" tests="9">
    <testcase classname="RestaurantStore Service Initialization should export a service instance" name="RestaurantStore Service Initialization should export a service instance" time="0.051">
    </testcase>
    <testcase classname="RestaurantStore Service Initialization should have required methods" name="RestaurantStore Service Initialization should have required methods" time="0.023">
    </testcase>
    <testcase classname="RestaurantStore Service Initialization should have data storage maps" name="RestaurantStore Service Initialization should have data storage maps" time="0.018">
    </testcase>
    <testcase classname="RestaurantStore Basic Functionality should be able to call initialize method" name="RestaurantStore Basic Functionality should be able to call initialize method" time="0.027">
    </testcase>
    <testcase classname="RestaurantStore Basic Functionality should be able to call getBrandRef method" name="RestaurantStore Basic Functionality should be able to call getBrandRef method" time="0.02">
    </testcase>
    <testcase classname="RestaurantStore Basic Functionality should be able to call getRestaurantRef method" name="RestaurantStore Basic Functionality should be able to call getRestaurantRef method" time="0.021">
    </testcase>
    <testcase classname="RestaurantStore Basic Functionality should be able to call getRestaurantsByBrand method" name="RestaurantStore Basic Functionality should be able to call getRestaurantsByBrand method" time="0.018">
    </testcase>
    <testcase classname="RestaurantStore Data Storage should have proper data structure" name="RestaurantStore Data Storage should have proper data structure" time="0.024">
    </testcase>
    <testcase classname="RestaurantStore Data Storage should handle empty data gracefully" name="RestaurantStore Data Storage should handle empty data gracefully" time="0.019">
    </testcase>
  </testsuite>
  <testsuite name="SessionIdGenerator" errors="0" failures="0" skipped="0" timestamp="2025-06-08T22:17:36" time="0.244" tests="15">
    <testcase classname="SessionIdGenerator generateOpaqueToken() should generate an opaque token" name="SessionIdGenerator generateOpaqueToken() should generate an opaque token" time="0.001">
    </testcase>
    <testcase classname="SessionIdGenerator generateOpaqueToken() should generate unique tokens" name="SessionIdGenerator generateOpaqueToken() should generate unique tokens" time="0.004">
    </testcase>
    <testcase classname="SessionIdGenerator generateOpaqueToken() should generate tokens with base64url format" name="SessionIdGenerator generateOpaqueToken() should generate tokens with base64url format" time="0.001">
    </testcase>
    <testcase classname="SessionIdGenerator generateOpaqueToken() should generate tokens of consistent length" name="SessionIdGenerator generateOpaqueToken() should generate tokens of consistent length" time="0.001">
    </testcase>
    <testcase classname="SessionIdGenerator generateOpaqueToken() should handle errors gracefully" name="SessionIdGenerator generateOpaqueToken() should handle errors gracefully" time="0.012">
    </testcase>
    <testcase classname="SessionIdGenerator validateToken() should validate a valid token" name="SessionIdGenerator validateToken() should validate a valid token" time="0.002">
    </testcase>
    <testcase classname="SessionIdGenerator validateToken() should reject null token" name="SessionIdGenerator validateToken() should reject null token" time="0.002">
    </testcase>
    <testcase classname="SessionIdGenerator validateToken() should reject undefined token" name="SessionIdGenerator validateToken() should reject undefined token" time="0.001">
    </testcase>
    <testcase classname="SessionIdGenerator validateToken() should reject non-string token" name="SessionIdGenerator validateToken() should reject non-string token" time="0.001">
    </testcase>
    <testcase classname="SessionIdGenerator validateToken() should reject empty string token" name="SessionIdGenerator validateToken() should reject empty string token" time="0.001">
    </testcase>
    <testcase classname="SessionIdGenerator validateToken() should reject invalid base64url token" name="SessionIdGenerator validateToken() should reject invalid base64url token" time="0.001">
    </testcase>
    <testcase classname="SessionIdGenerator validateToken() should reject token with wrong length" name="SessionIdGenerator validateToken() should reject token with wrong length" time="0.001">
    </testcase>
    <testcase classname="SessionIdGenerator Token Security should generate cryptographically secure tokens" name="SessionIdGenerator Token Security should generate cryptographically secure tokens" time="0.009">
    </testcase>
    <testcase classname="SessionIdGenerator Token Security should generate tokens that are URL-safe" name="SessionIdGenerator Token Security should generate tokens that are URL-safe" time="0.001">
    </testcase>
    <testcase classname="SessionIdGenerator Token Security should generate tokens without padding" name="SessionIdGenerator Token Security should generate tokens without padding" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="MessageBuilders" errors="0" failures="0" skipped="0" timestamp="2025-06-08T22:17:36" time="0.265" tests="16">
    <testcase classname="MessageBuilders buildBasicTextMessageData() should build basic text message for notification" name="MessageBuilders buildBasicTextMessageData() should build basic text message for notification" time="0.004">
    </testcase>
    <testcase classname="MessageBuilders buildBasicTextMessageData() should build basic text message for dialog" name="MessageBuilders buildBasicTextMessageData() should build basic text message for dialog" time="0.002">
    </testcase>
    <testcase classname="MessageBuilders buildBasicTextMessageData() should default to notification message type" name="MessageBuilders buildBasicTextMessageData() should default to notification message type" time="0.007">
    </testcase>
    <testcase classname="MessageBuilders buildBasicTextMessageData() should handle empty text message" name="MessageBuilders buildBasicTextMessageData() should handle empty text message" time="0.004">
    </testcase>
    <testcase classname="MessageBuilders buildBasicTextMessageData() should handle special characters in text" name="MessageBuilders buildBasicTextMessageData() should handle special characters in text" time="0.005">
    </testcase>
    <testcase classname="MessageBuilders buildQuickReplyMessageData() should build quick reply message with buttons" name="MessageBuilders buildQuickReplyMessageData() should build quick reply message with buttons" time="0.002">
    </testcase>
    <testcase classname="MessageBuilders buildQuickReplyMessageData() should handle single button" name="MessageBuilders buildQuickReplyMessageData() should handle single button" time="0.001">
    </testcase>
    <testcase classname="MessageBuilders buildQuickReplyMessageData() should handle header and footer" name="MessageBuilders buildQuickReplyMessageData() should handle header and footer" time="0.001">
    </testcase>
    <testcase classname="MessageBuilders buildQuickReplyMessageData() should throw error for invalid button count" name="MessageBuilders buildQuickReplyMessageData() should throw error for invalid button count" time="0.01">
    </testcase>
    <testcase classname="MessageBuilders buildWhatsAppTemplateMessageData() should build template message with variables" name="MessageBuilders buildWhatsAppTemplateMessageData() should build template message with variables" time="0.002">
    </testcase>
    <testcase classname="MessageBuilders buildWhatsAppTemplateMessageData() should handle template with image" name="MessageBuilders buildWhatsAppTemplateMessageData() should handle template with image" time="0.002">
    </testcase>
    <testcase classname="MessageBuilders buildWhatsAppTemplateMessageData() should handle empty options" name="MessageBuilders buildWhatsAppTemplateMessageData() should handle empty options" time="0.001">
    </testcase>
    <testcase classname="MessageBuilders buildDialogueMessageData() should build dialogue text message" name="MessageBuilders buildDialogueMessageData() should build dialogue text message" time="0.006">
    </testcase>
    <testcase classname="MessageBuilders buildDialogueMessageData() should handle empty text" name="MessageBuilders buildDialogueMessageData() should handle empty text" time="0.001">
    </testcase>
    <testcase classname="MessageBuilders Message Structure Validation should create valid notification message structure" name="MessageBuilders Message Structure Validation should create valid notification message structure" time="0.004">
    </testcase>
    <testcase classname="MessageBuilders Message Structure Validation should create valid dialogue message structure" name="MessageBuilders Message Structure Validation should create valid dialogue message structure" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="WhatsAppService" errors="0" failures="0" skipped="0" timestamp="2025-06-08T22:17:37" time="0.118" tests="8">
    <testcase classname="WhatsAppService Service Initialization should export a service instance" name="WhatsAppService Service Initialization should export a service instance" time="0.002">
    </testcase>
    <testcase classname="WhatsAppService Service Initialization should have required methods" name="WhatsAppService Service Initialization should have required methods" time="0.001">
    </testcase>
    <testcase classname="WhatsAppService Service Initialization should have configuration properties or be configurable" name="WhatsAppService Service Initialization should have configuration properties or be configurable" time="0.001">
    </testcase>
    <testcase classname="WhatsAppService Service Initialization should have message queue or queueing capability" name="WhatsAppService Service Initialization should have message queue or queueing capability" time="0.001">
    </testcase>
    <testcase classname="WhatsAppService Service Initialization should have retry configuration or error handling" name="WhatsAppService Service Initialization should have retry configuration or error handling" time="0.001">
    </testcase>
    <testcase classname="WhatsAppService Basic Functionality should be able to call getAccessToken method" name="WhatsAppService Basic Functionality should be able to call getAccessToken method" time="0.001">
    </testcase>
    <testcase classname="WhatsAppService Basic Functionality should be able to call sendBasicText method" name="WhatsAppService Basic Functionality should be able to call sendBasicText method" time="0.002">
    </testcase>
    <testcase classname="WhatsAppService Basic Functionality should be able to call sendQuickReply method" name="WhatsAppService Basic Functionality should be able to call sendQuickReply method" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="LinkGenerator" errors="0" failures="0" skipped="0" timestamp="2025-06-08T22:17:37" time="0.107" tests="6">
    <testcase classname="LinkGenerator generateMenuLink() should generate menu link with token and orderURL" name="LinkGenerator generateMenuLink() should generate menu link with token and orderURL" time="0.001">
    </testcase>
    <testcase classname="LinkGenerator generateMenuLink() should handle different domains" name="LinkGenerator generateMenuLink() should handle different domains" time="0.001">
    </testcase>
    <testcase classname="LinkGenerator generateMenuLink() should handle special characters in token" name="LinkGenerator generateMenuLink() should handle special characters in token" time="0.001">
    </testcase>
    <testcase classname="LinkGenerator generateAddressLink() should generate address link with token and orderURL" name="LinkGenerator generateAddressLink() should generate address link with token and orderURL" time="0.001">
    </testcase>
    <testcase classname="LinkGenerator generateAddressLink() should handle different domains" name="LinkGenerator generateAddressLink() should handle different domains" time="0">
    </testcase>
    <testcase classname="LinkGenerator generateAddressLink() should handle special characters in token" name="LinkGenerator generateAddressLink() should handle special characters in token" time="0">
    </testcase>
  </testsuite>
</testsuites>