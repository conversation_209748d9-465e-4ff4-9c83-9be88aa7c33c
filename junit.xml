<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="8" failures="3" errors="0" time="0.885">
  <testsuite name="WhatsAppService" errors="0" failures="3" skipped="0" timestamp="2025-06-07T22:02:01" time="0.47" tests="8">
    <testcase classname="WhatsAppService Service Initialization should export a service instance" name="WhatsAppService Service Initialization should export a service instance" time="0.003">
    </testcase>
    <testcase classname="WhatsAppService Service Initialization should have required methods" name="WhatsAppService Service Initialization should have required methods" time="0">
    </testcase>
    <testcase classname="WhatsAppService Service Initialization should have configuration properties" name="WhatsAppService Service Initialization should have configuration properties" time="0.002">
      <failure>Error: expect(received).toBeDefined()

Received: undefined
    at Object.toBeDefined (/home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js:30:38)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="WhatsAppService Service Initialization should have message queue" name="WhatsAppService Service Initialization should have message queue" time="0">
      <failure>Error: expect(received).toBeDefined()

Received: undefined
    at Object.toBeDefined (/home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js:36:44)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="WhatsAppService Service Initialization should have retry configuration" name="WhatsAppService Service Initialization should have retry configuration" time="0.001">
      <failure>Error: expect(received).toBeDefined()

Received: undefined
    at Object.toBeDefined (/home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/services/whatsappService.test.js:40:43)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="WhatsAppService Basic Functionality should be able to call getAccessToken method" name="WhatsAppService Basic Functionality should be able to call getAccessToken method" time="0.001">
    </testcase>
    <testcase classname="WhatsAppService Basic Functionality should be able to call sendBasicText method" name="WhatsAppService Basic Functionality should be able to call sendBasicText method" time="0">
    </testcase>
    <testcase classname="WhatsAppService Basic Functionality should be able to call sendQuickReply method" name="WhatsAppService Basic Functionality should be able to call sendQuickReply method" time="0">
    </testcase>
  </testsuite>
</testsuites>