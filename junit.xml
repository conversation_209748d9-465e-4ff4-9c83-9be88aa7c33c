<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="9" failures="1" errors="0" time="2.184">
  <testsuite name="Restaurant GraphQL API Integration Tests" errors="0" failures="1" skipped="8" timestamp="2025-06-08T12:54:11" time="2.15" tests="9">
    <testcase classname="Restaurant GraphQL API Integration Tests GraphQL Endpoint should respond to GraphQL endpoint" name="Restaurant GraphQL API Integration Tests GraphQL Endpoint should respond to GraphQL endpoint" time="0.003">
      <failure>TypeError: app.address is not a function
    at Test.serverAddress (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/supertest/lib/test.js:46:22)
    at new Test (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/supertest/lib/test.js:34:14)
    at Object.obj.&lt;computed&gt; [as post] (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/supertest/index.js:43:18)
    at post (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js:14:10)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:252:3)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:126:9)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Restaurant GraphQL API Integration Tests GraphQL Endpoint should handle invalid GraphQL queries" name="Restaurant GraphQL API Integration Tests GraphQL Endpoint should handle invalid GraphQL queries" time="0">
      <skipped/>
    </testcase>
    <testcase classname="Restaurant GraphQL API Integration Tests Restaurant Schema should have Restaurant type in schema" name="Restaurant GraphQL API Integration Tests Restaurant Schema should have Restaurant type in schema" time="0">
      <skipped/>
    </testcase>
    <testcase classname="Restaurant GraphQL API Integration Tests Basic GraphQL Operations should handle simple queries" name="Restaurant GraphQL API Integration Tests Basic GraphQL Operations should handle simple queries" time="0">
      <skipped/>
    </testcase>
    <testcase classname="Restaurant GraphQL API Integration Tests Basic GraphQL Operations should validate GraphQL syntax" name="Restaurant GraphQL API Integration Tests Basic GraphQL Operations should validate GraphQL syntax" time="0">
      <skipped/>
    </testcase>
    <testcase classname="Restaurant GraphQL API Integration Tests Basic GraphQL Operations should handle empty queries" name="Restaurant GraphQL API Integration Tests Basic GraphQL Operations should handle empty queries" time="0">
      <skipped/>
    </testcase>
    <testcase classname="Restaurant GraphQL API Integration Tests Schema Introspection should support schema introspection" name="Restaurant GraphQL API Integration Tests Schema Introspection should support schema introspection" time="0">
      <skipped/>
    </testcase>
    <testcase classname="Restaurant GraphQL API Integration Tests Schema Introspection should list available queries" name="Restaurant GraphQL API Integration Tests Schema Introspection should list available queries" time="0">
      <skipped/>
    </testcase>
    <testcase classname="Restaurant GraphQL API Integration Tests Schema Introspection should list available mutations" name="Restaurant GraphQL API Integration Tests Schema Introspection should list available mutations" time="0">
      <skipped/>
    </testcase>
  </testsuite>
</testsuites>