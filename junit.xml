<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="19" failures="0" errors="0" time="3.198">
  <testsuite name="SessionService" errors="0" failures="0" skipped="0" timestamp="2025-06-08T12:39:30" time="2.532" tests="11">
    <testcase classname="SessionService Service Initialization should export a service instance" name="SessionService Service Initialization should export a service instance" time="0.491">
    </testcase>
    <testcase classname="SessionService Service Initialization should have required methods" name="SessionService Service Initialization should have required methods" time="0.161">
    </testcase>
    <testcase classname="SessionService Service Initialization should have Redis client" name="SessionService Service Initialization should have Redis client" time="0.151">
    </testcase>
    <testcase classname="SessionService Service Initialization should have session queues map" name="SessionService Service Initialization should have session queues map" time="0.195">
    </testcase>
    <testcase classname="SessionService Basic Functionality should be able to call initializeContext method" name="SessionService Basic Functionality should be able to call initializeContext method" time="0.145">
    </testcase>
    <testcase classname="SessionService Basic Functionality should be able to call createSession method" name="SessionService Basic Functionality should be able to call createSession method" time="0.155">
    </testcase>
    <testcase classname="SessionService Basic Functionality should be able to call getSession method" name="SessionService Basic Functionality should be able to call getSession method" time="0.155">
    </testcase>
    <testcase classname="SessionService Basic Functionality should be able to call updateSession method" name="SessionService Basic Functionality should be able to call updateSession method" time="0.155">
    </testcase>
    <testcase classname="SessionService Basic Functionality should be able to call deleteSession method" name="SessionService Basic Functionality should be able to call deleteSession method" time="0.18">
    </testcase>
    <testcase classname="SessionService Context Initialization should initialize context with proper structure" name="SessionService Context Initialization should initialize context with proper structure" time="0.153">
    </testcase>
    <testcase classname="SessionService Context Initialization should merge custom context with defaults" name="SessionService Context Initialization should merge custom context with defaults" time="0.149">
    </testcase>
  </testsuite>
  <testsuite name="WhatsAppService" errors="0" failures="0" skipped="0" timestamp="2025-06-08T12:39:33" time="0.121" tests="8">
    <testcase classname="WhatsAppService Service Initialization should export a service instance" name="WhatsAppService Service Initialization should export a service instance" time="0.001">
    </testcase>
    <testcase classname="WhatsAppService Service Initialization should have required methods" name="WhatsAppService Service Initialization should have required methods" time="0">
    </testcase>
    <testcase classname="WhatsAppService Service Initialization should have configuration properties or be configurable" name="WhatsAppService Service Initialization should have configuration properties or be configurable" time="0.001">
    </testcase>
    <testcase classname="WhatsAppService Service Initialization should have message queue or queueing capability" name="WhatsAppService Service Initialization should have message queue or queueing capability" time="0">
    </testcase>
    <testcase classname="WhatsAppService Service Initialization should have retry configuration or error handling" name="WhatsAppService Service Initialization should have retry configuration or error handling" time="0">
    </testcase>
    <testcase classname="WhatsAppService Basic Functionality should be able to call getAccessToken method" name="WhatsAppService Basic Functionality should be able to call getAccessToken method" time="0.001">
    </testcase>
    <testcase classname="WhatsAppService Basic Functionality should be able to call sendBasicText method" name="WhatsAppService Basic Functionality should be able to call sendBasicText method" time="0">
    </testcase>
    <testcase classname="WhatsAppService Basic Functionality should be able to call sendQuickReply method" name="WhatsAppService Basic Functionality should be able to call sendQuickReply method" time="0.001">
    </testcase>
  </testsuite>
</testsuites>